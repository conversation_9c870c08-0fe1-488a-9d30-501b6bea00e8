package com.wosai.bsm.enterprise;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/business-config.xml"})
@WebAppConfiguration
public class EnterpriseTest3 {
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseTest3.class);


    @Autowired
    JdbcTemplate jdbcTemplate;
    @Test
    public void up() {
        logger.info("start up");
        jdbcTemplate.execute("select 1");
        logger.info("start end");

    }

}
