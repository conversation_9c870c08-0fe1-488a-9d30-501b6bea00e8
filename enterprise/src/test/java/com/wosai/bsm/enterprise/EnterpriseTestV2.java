package com.wosai.bsm.enterprise;

import com.alibaba.fastjson.JSONObject;
import com.wosai.bsm.enterprise.biz.LarkClient;
import com.wosai.bsm.enterprise.client.alipay.AliPayTradeClient;
import com.wosai.bsm.enterprise.client.alipay.AliPayTradeConfig;
import com.wosai.bsm.enterprise.client.crland.CrLandClient;
import com.wosai.bsm.enterprise.client.hdcre.HDCREClient;
import com.wosai.bsm.enterprise.client.hdcre.HDCREConfig;
import com.wosai.bsm.enterprise.client.icd.ICDClient;
import com.wosai.bsm.enterprise.client.techtrans.TechTransClient;
import com.wosai.bsm.enterprise.client.techtransV2.TechTransV2Client;
import com.wosai.bsm.enterprise.client.techtransV2.TechTransV2Config;
import com.wosai.bsm.enterprise.client.techtransV3.TechTransV3Client;
import com.wosai.bsm.enterprise.service.TradeNotifyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/3/23、16:49
 **/


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/business-config.xml"})
public class EnterpriseTestV2 {

    @Autowired
    private TradeNotifyService tradeNotifyService;

    @Autowired
    ICDClient icdClient;


    @Autowired
    CrLandClient crLandClient;

    @Autowired
    HDCREClient HDCREClient;


    @Autowired
    TechTransV2Client techTransV2Client;

    @Autowired
    TechTransV3Client techTransV3Client;


    @Autowired
    TechTransClient techTransClient;

    @Autowired
    private LarkClient larkClient;


    @Autowired
    private AliPayTradeClient aliPayTradeClient;

//    @Autowired
//    private SheetUtil sheetUtil;

    @Test
    public void notifyTest() throws Exception {

//       华润签名测试
//        String s="Api_ID=mixc.imPOSWBJB.GLWXCJB.orderCollect&Api_Version=1.0.0&App_Pub_ID=10000133301US&App_Sub_ID=10000133334PY&App_Token=c861e8a4be0f41b182abdb55b986444a&Format=json&Partner_ID=70000006&REQUEST_DATA={\"cashierId\":\"20028hvgl120n0101\",\"checkCode\":\"p88888888\",\"mall\":\"20028\",\"orderId\":\"1081983263\",\"payList\":[{\"discountAmt\":7.96,\"payAmt\":61,\"paymentMethod\":\"OT\",\"time\":\"20220405000000\",\"value\":53.04}],\"store\":\"HVGL120N01\",\"tillId\":\"01\",\"time\":\"20220405105628\",\"totalAmt\":\"53.04\",\"type\":\"SALE\"}&Sign_Method=md5&Sys_ID=100001333&Time_Stamp=2022-04-20 17:37:50:583&0bca40d57d1f44208d787a4e0a87957d";
//
//        String md5EN = DigestUtils.md5DigestAsHex(s.getBytes(StandardCharsets.UTF_8)).toUpperCase();
//        System.out.println(md5EN);
//
//        String s2="491F3087452F1965020F286193239750";
//        System.out.println(md5EN.equals(s2));

//        环茂测试case
        Map transactionMap = JSONObject.parseObject("{\n" +
                "  \"subject\": \"岑溪市果唯伊水果店\",\n" +
                "  \"buyer_login\": \"oc5mSjkO3SuZazLXsMbLVdohLzhg\",\n" +
                "  \"merchant_id\": \"674fe667-81d0-489b-954c-ea09a14a9bde\",\n" +
                "  \"mtime\": 1744341393267,\n" +
                "  \"type\": 30,\n" +
                "  \"body\": null,\n" +
                "  \"tsn\": \"****************\",\n" +
                "  \"operator\": 10,\n" +
                "  \"product_flag\": \"au\",\n" +
                "  \"extra_out_fields\": {\n" +
                "    \"payments\": [\n" +
                "      {\n" +
                "        \"type\": \"WALLET_WEIXIN\",\n" +
                "        \"origin_type\": \"OTHERS\",\n" +
                "        \"amount\": 4840\n" +
                "      }\n" +
                "    ],\n" +
                "    \"wallet_account_type\": 1,\n" +
                "    \"combo_id\": 4,\n" +
                "    \"weixin_appid\": \"wx72534f3638c59073\",\n" +
                "    \"is_default_poi\": false\n" +
                "  },\n" +
                "  \"reflect\": null,\n" +
                "  \"original_amount\": 4840,\n" +
                "  \"ctime\": *************,\n" +
                "  \"terminal_id\": \"d8d461c0-ff31-4037-a9c5-ee3bac9f79d4\",\n" +
                "  \"client_tsn\": \"42E2A2BB270B6E3FAB28E5C5BBC036A5\",\n" +
                "  \"store_id\": \"625227c0-6773-4b2e-8393-223733e63cd2\",\n" +
                "  \"extra_params\": {\n" +
                "    \"sqb_ip\": \"***************\",\n" +
                "    \"client_ip\": \"***************\",\n" +
                "    \"poi\": {\n" +
                "      \"latitude\": 22.9143069,\n" +
                "      \"longitude\": 110.984722\n" +
                "    },\n" +
                "    \"barcode\": \"132776175738389777\",\n" +
                "    \"sqb_mac_id\": \"487D2E0C7E07\"\n" +
                "  },\n" +
                "  \"payway\": 3,\n" +
                "  \"finish_time\": 1744341395216,\n" +
                "  \"sub_payway\": 1,\n" +
                "  \"config_snapshot\": {\n" +
                "    \"clearance_provider\": 7,\n" +
                "    \"latitude\": 22.915335,\n" +
                "    \"store_client_sn\": null,\n" +
                "    \"merchant_id\": \"674fe667-81d0-489b-954c-ea09a14a9bde\",\n" +
                "    \"merchant_sn\": \"1680007438878\",\n" +
                "    \"merchant_monthly_max_credit_limit_trans\": null,\n" +
                "    \"pay_status\": 1,\n" +
                "    \"merchant_daily_max_credit_limit_trans\": null,\n" +
                "    \"provider\": 1037,\n" +
                "    \"union_over_seas_wallet_day_tran_limit\": 10000,\n" +
                "    \"store_name\": \"岑溪市果唯伊水果店\",\n" +
                "    \"currency\": \"CNY\",\n" +
                "    \"terminal_id\": \"d8d461c0-ff31-4037-a9c5-ee3bac9f79d4\",\n" +
                "    \"terminal_sn\": \"100029850044523196\",\n" +
                "    \"longitude\": 110.984002,\n" +
                "    \"store_id\": \"625227c0-6773-4b2e-8393-223733e63cd2\",\n" +
                "    \"store_sn\": \"1580000009520391\",\n" +
                "    \"channel_name\": \"上海收钱吧互联网科技股份有限公司\",\n" +
                "    \"store_city\": \"梧州市\",\n" +
                "    \"trade_app\": 1,\n" +
                "    \"terminal_vendor_app_appid\": \"2024062000007227\",\n" +
                "    \"terminal_category\": 102,\n" +
                "    \"union_over_seas_wallet_single_tran_limit\": 500,\n" +
                "    \"merchant_name\": \"岑溪市果唯伊水果店\",\n" +
                "    \"payway_day_credit_limits\": null,\n" +
                "    \"term_info\": {\n" +
                "      \"term_id\": \"SH06L8DI\",\n" +
                "      \"term_type\": null,\n" +
                "      \"serial_num\": null\n" +
                "    },\n" +
                "    \"term_id\": \"SH06L8DI\",\n" +
                "    \"haike_up_trade_params\": {\n" +
                "      \"is_affiliated\": false,\n" +
                "      \"weixin_sub_appsecret\": \"\\\"h<h<\\\"`Z\\\"efh}<f\\\"[[\",\n" +
                "      \"fee_rate_tag\": {\n" +
                "        \"1\": \"4:\"\n" +
                "      },\n" +
                "      \"fee\": 15,\n" +
                "      \"cert_id\": \"4550759066\",\n" +
                "      \"merchant_name\": \"岑溪市果唯伊水果店（个体工商户）\",\n" +
                "      \"active\": true,\n" +
                "      \"agent_no\": \"ISV002571\",\n" +
                "      \"weixin_appid\": \"wx17728c1a8fa300d6\",\n" +
                "      \"weixin_mini_sub_appid\": \"wxccbcac9a3ece5112\",\n" +
                "      \"fee_rate\": 0.3,\n" +
                "      \"weixin_sub_mch_id\": \"752836321\",\n" +
                "      \"liquidation_next_day\": true,\n" +
                "      \"original_provider_mch_id\": \"\",\n" +
                "      \"weixin_sub_appid\": \"wx72534f3638c59073\",\n" +
                "      \"access_key\": \"@y1[`\\\"n[be1nzb<<ffbzy<yb``'ee8<f}<[z\",\n" +
                "      \"weixin_mch_id\": \"**********\",\n" +
                "      \"weixin_mini_sub_appsecret\": \"\",\n" +
                "      \"channel_id\": 176174614,\n" +
                "      \"provider_mch_id\": \"833F622154990055\"\n" +
                "    },\n" +
                "    \"payway_month_credit_limits\": null,\n" +
                "    \"terminal_name\": \"岑溪市果唯伊水果店\",\n" +
                "    \"district_code\": \"450481\",\n" +
                "    \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "    \"common_switch\": \"00000000000100000000002222222222\",\n" +
                "    \"merchant_country\": \"CHN\",\n" +
                "    \"is_need_refund_fee_flag\": null,\n" +
                "    \"hit_payway\": null\n" +
                "  },\n" +
                "  \"effective_amount\": 4840,\n" +
                "  \"trade_no\": \"4200002606202504116640808529\",\n" +
                "  \"channel_finish_time\": 1744341394000,\n" +
                "  \"items\": null,\n" +
                "  \"order_sn\": \"****************\",\n" +
                "  \"buyer_uid\": \"oGFfkszlBk-piCFwzFvehJVJSlE4\",\n" +
                "  \"status\": 2000\n" +
                "}\n", Map.class);
////        // 华润置地测试
        Map transactionMap1 = JSONObject.parseObject("{\"subject\":\"充值退款\",\"merchant_id\":\"6de0a314-39a4-4e01-bf4d-07fc806af472\",\"mtime\":1717566404520,\"type\":30,\"body\":\"充值退款\",\"tsn\":\"202406051346440906366263\",\"operator\":null,\"product_flag\":\"a1\",\"extra_out_fields\":null,\"reflect\":null,\"original_amount\":247,\"ctime\":1717566404520,\"terminal_id\":\"105f205b-3093-4aa4-9e65-2064948688a1\",\"client_tsn\":\"R1798229950011876600\",\"store_id\":\"2f68218c5b18-9c4a-55e4-6f32-2dfc4ffe\",\"payway\":102,\"finish_time\":1717566404520,\"sub_payway\":1,\"config_snapshot\":{\"store_id\":\"2f68218c5b18-9c4a-55e4-6f32-2dfc4ffe\",\"store_sn\":\"21590000000917355\",\"store_city\":\"苏州市\",\"clearance_provider\":2,\"terminal_vendor_app_appid\":\"2019012400021738\",\"latitude\":\"31.309188\",\"merchant_name\":\"113359通联\",\"merchant_id\":\"64b4e94a-1bb6-4679-ba90-6a73e06ba23f\",\"merchant_sn\":\"21690003966948\",\"pay_status\":1,\"terminal_name\":\"业务开通门店名称03091133的储值充值终端\",\"district_code\":\"320506\",\"vendor_id\":\"7bd7fb90-161f-4bed-a7e6-0fb4ead63aec\",\"sharing_switch\":1,\"common_switch\":\"01000000000222222222222222222222\",\"merchant_country\":\"CHN\",\"store_name\":\"业务开通门店名称03091133\",\"currency\":\"CNY\",\"charging_trade_params\":{\"fee\":0,\"active\":true,\"fee_rate\":\"0\"},\"terminal_id\":\"105f205b-3093-4aa4-9e65-2064948688a1\",\"terminal_sn\":\"2101217380090473870\",\"longitude\":\"120.776461\"},\"effective_amount\":247,\"trade_no\":null,\"channel_finish_time\":1717566404520,\"items\":null,\"order_sn\":\"202406051346446366263\",\"buyer_uid\":\"b4e8eb0e-8d95-11ed-afbc-506b4b2f3384\",\"status\":2000}", Map.class);
        Map m = JSONObject.parseObject(" {\n" +
                "            \"object_type\": 6,\n" +
                "            \"object_id\": \"526d6382-1b5e-4c5b-974f-a67c2bc85217\",\n" +
                "            \"app_id\": \"techtrans\",\n" +
                "            \"app_secret\": \"\",\n" +
                "            \"notify_url\": \"http://sjcj.cwtc.com:8185/SalesTrans/rest/salestransaction/salestranslitev61\",\n" +
                "            \"notify_gateway\": \"\",\n" +
                "            \"notify_status\": \"\",\n" +
                "            \"notify_resp\": \"success\",\n" +
                "            \"notify_retry\": \"60,300,600\",\n" +
                "            \"merchant_sn\": \"1680007497672\",\n" +
                "            \"notify_type\": 0,\n" +
                "            \"notify_payway_type\": 0,\n" +
                "            \"extra\": {\n" +
                "                \"storeCode\": \"03000002\",\n" +
                "                \"cashier\": \"03000002\",\n" +
                "                \"itemCode\": \"03000002\",\n" +
                "                \"apiKey\": \"CRMINT\",\n" +
                "                \"tillId\": \"01\",\n" +
                "                \"itemOrgId\": \"000033\",\n" +
                "                \"baseCurrencyCode\": \"RMB\",\n" +
                "                \"cash\":true\n" +
                "            }\n" +
                "        }", Map.class);
//        tradeNotifyService.notifyTrade(transactionMap);

//        tradeNotifyService.notifyTrade(transactionMap);

        Map payLoad = JSONObject.parseObject("{\n" +
                "    \"subject\": \"充值退款\",\n" +
                "    \"payer_uid\": \"b4e8eb0e-8d95-11ed-afbc-506b4b2f3384\",\n" +
                "    \"merchant_sn\": \"1680007438878\",\n" +
                "    \"type\": \"TYPE_PAYMENT\",\n" +
                "    \"client_sn\": \"R1798229951160800\",\n" +
                "    \"operator\": \"\",\n" +
                "    \"tsn\": \"2024060513467409921887274\",\n" +
                "    \"order_status\": \"PAID\",\n" +
                "    \"reflect\": null,\n" +
                "    \"is_stored_in\": true,\n" +
                "    \"is_stored_pay\": true,\n" +
                "    \"ctime\": \"1750056395000\",\n" +
                "    \"sn\": \"2000259900900000008091\",\n" +
                "    \"id\": 195001,\n" +
                "    \"push_date\": \"2024-08-02\",\n" +
                "    \"terminal_id\": \"105f205b-3093-4aa4-9e65-2064948688a1\",\n" +
                "    \"terminal_sn\": \"2101217380090473870\",\n" +
                "    \"sqb_platform_discount_amount\": 0,\n" +
                "    \"client_tsn\": \"R1798229951160800\",\n" +
                "    \"store_id\": \"2f68218c5b18-9c4a-55e4-6f32-2dfc4ffe\",\n" +
                "    \"store_sn\": \"st-1580000001746089\",\n" +
                "    \"device_fingerprint\": \"stored97430408-d64c-4549-b35a-f3a9e4143619\",\n" +
                "                \"merchant_id\": \"6de0a314-39a4-4e01-bf4d-07fc806af472\",\n" +
                "    \"amount\": \"10\",\n" +
                "    \"push_status\": 4,\n" +
                "    \"device_sn\": \"stored97430408-d64c-4549-b35a-f3a9e4143619\",\n" +
                "    \"payway\": \"1\",\n" +
                "    \"notify_url\": \"http://ztopenapiuat.crland.com.cn/api-gateway/rs-service/\",\n" +
                "    \"object_id\": \"2f68218c5b18-9c4a-55e4-6f32-2dfc4ffe\",\n" +
                "    \"version\": 1,\n" +
                "    \"finish_time\": \"1750056395000\",\n" +
                "    \"sub_payway\": \"1\",\n" +
                "    \"push_count\": 0,\n" +
                "    \"total_amount\": \"124\",\n" +
                "    \"channel_finish_time\": \"1750056395000\",\n" +
                "    \"settlement_amount\": 247,\n" +
                "    \"trade_no\": \"\",\n" +
                "    \"net_amount\": \"247\",\n" +
                "    \"sqb_mch_discount_amount\": 0,\n" +
                "    \"push_time\": 1750056395000,\n" +
                "    \"status\": \"SUCCESS\"\n" +
                "}");

//        tradeNotifyService.notifyTrade(transactionMap1);

//        CrLandConfig crLandConfig = JSONObject.parseObject("{\n" +
//                "    \"crLandMall\": {\n" +
//                "        \"apiId\": \"mixc.imPOSWBJB.GLWXCJB.orderCollect\",\n" +
//                "        \"appPubId\": \"10000187223RL\",\n" +
//                "        \"sysId\": \"100001872\",\n" +
//                "        \"appSubId\": \"10000187223SB\",\n" +
//                "        \"partnerId\": \"70000029\",\n" +
//                "        \"appToken\": \"1f9d5d857b2a4e78a239c24c0727e2bc\",\n" +
//                "        \"apiVersion\": \"1.0.1\",\n" +
//                "        \"token\": \"1f9d5d857b2a4e78a239c24c0727e2bc\",\n" +
//                "        \"signToken\": \"abf630c2d91d4343a5be78152396c4b5\"\n" +
//                "    },\n" +
//                "    \"crLandStore\": {\n" +
//                "        \"cashierId\": \"20144gngb203n0101\",\n" +
//                "        \"checkCode\": \"p88888888\",\n" +
//                "        \"mall\": \"20144\",\n" +
//                "        \"tillId\": \"01\",\n" +
//                "        \"store\": \"GNGB203N01\"\n" +
//                "    }\n" +
//                "}", CrLandConfig.class);
//
//
//        ICDConfig icdConfig = JSONObject.parseObject("{\n" +
//                "        \"storeCode\": \"B224702\",\n" +
//                "        \"cashier\": \"b22470201\",\n" +
//                "        \"itemCode\": \"b22470201\"\n" +
//                "    }", ICDConfig.class);
//
//
        TechTransV2Config techTransConfig = JSONObject.parseObject("{\n" +
                "    \"mallId\": \"Heartland\",\n" +
                "    \"itemCode\": \"and000000001\",\n" +
                "    \"storeCode\": \"999\",\n" +
                "    \"username\": \"\",\n" +
                "    \"password\": \"\",\n" +
                "    \"licenseKey\": \"\",\n" +
                "    \"messageType\": \"SALESDATA\",\n" +
                "    \"messageId\": \"332\",\n" +
                "    \"version\": \"V332M\",\n" +
                "    \"paywayMap\": {\n" +
                "        \"1\": \"XI\",\n" +
                "        \"2\": \"XI\",\n" +
                "        \"3\": \"XF\",\n" +
                "        \"102\": \"CH\",\n" +
                "        \"other\": \"OH\"\n" +
                "    }\n" +
                "}", TechTransV2Config.class);
//
        HDCREConfig hdcreConfig = new HDCREConfig();
        hdcreConfig.setPosNo("010015");
        Map<String, Object> map = new HashMap<>();
        map.put("102", "01");
        map.put("other", "02");
        hdcreConfig.setPayWayMap(map);
//
//        String icdURL = "https://posapi.icdmall.com:8185/posservice/rest/salestransaction/salestranslitev61";
//
//        String crLandURL = "http://ztopenapi.crland.com.cn/api-gateway/rs-service/";
//
//        String hdcreUrl= "http://121.15.10.11:8280/cre-agency-server/rest/esc/1/buy/save";
//
//        String techTransUrl= "http://sjcj.cwtc.com:8185/SalesTrans/rest/salestransaction/salestranslitev61";
//
//
//        String techTransUrl2= "http://xsapi.kingkeybanner.com:8989/SalesTransWoSign/rest/salestransaction/salestranslitev61";
//
//
//        String techTransV2Url= "http://posme.the-place.com.cn:8080/chiatai/salestrans.asmx?wsdl";
//
//
////        icdClient.send(icdURL, icdConfig, payLoad);
//        crLandClient.send(crLandURL, crLandConfig, payLoad);
//        HDCREClient.send("http://124.71.209.28:8280/cre-agency-server/rest/esc/1/buy/save",hdcreConfig,payLoad);
//        techTransConfig.setApiKey("KKONE");
//        techTransConfig.setStoreCode("13B156a2");
//        techTransConfig.setTillId("00");
//        techTransConfig.setCashier("13230014011");
//        techTransConfig.setItemCode("13230014011");
//        techTransConfig.setItemOrgId("002668");
////        techTransConfig.setBaseCurrencyCode("RMB");
//        techTransConfig.setCash(true);
////
////        // 科传2
//      techTransClient.send(techTransUrl2,techTransConfig,payLoad);


        TechTransV2Config techTransConfig2 = JSONObject.parseObject("{\n" +
                "    \"mallId\": \"Heartland\",\n" +
                "    \"itemCode\": \"and00000001\",\n" +
                "    \"storeCode\": \"999\",\n" +
                "    \"username\": \"\",\n" +
                "    \"password\": \"\",\n" +
                "    \"licenseKey\": \"\",\n" +
                "    \"messageType\": \"SALESDATA\",\n" +
                "    \"messageId\": \"332\",\n" +
                "    \"version\": \"V332M\",\n" +
                "    \"cashier\": \"99901\",\n" +
                "    \"payWayMap\": {\n" +
                "        \"1\": \"XI\",\n" +
                "        \"2\": \"XI\",\n" +
                "        \"3\": \"XF\",\n" +
                "        \"102\": \"CH\",\n" +
                "        \"other\": \"OH\"\n" +
                "    },\n" +
                "    \"includeStoreIn\": true,\n" +
                "    \"includeStorePay\": false\n" +
                "}", TechTransV2Config.class);

//        techTransV2Config.setUsername("010201");
//        techTransV2Config.setPassword("010201");
//        techTransV2Config.setLicenseKey("");
//        techTransV2Config.setMallId("TP1RE");
//        techTransV2Config.setStoreCode("TP1REL21107");
//        techTransV2Config.setItemCode("TP1REL211071");
//        techTransV2Config.setMessageId("332");
//        techTransV2Config.setMessageType("SALESDATA");
//        techTransV2Config.setVersion("V332M");
//        techTransV2Config.setLicenseKey("");
        techTransV2Client.send("https://htlpos.hanglung.com.cn:8280/HLD/salestrans12.asmx", techTransConfig2, payLoad);
////        Thread.sleep(1000000L);
//
////        larkClient.sendMsg("华为数据","33");
//
////        sheetUtil.sendSummaryListAsExcel(new ArrayList<>(),"");
//
////
////        programName: APIEXTERNAL
////        DeviceId： 01
////        activationCode: 66FA4D4DB5AB4922597E22738682AAA5
////        locationCode: B1-1800001
////        programName: APIEXTERNAL
////        storeCode: B1-1800001
////        tillId: 01
////        cashier: cashierJ001
////        itemCode: B1-1800001034034
////        tenderCode: CH
////        baseCurrencyCode: RMB
//
////        TechTransV3Config techTransV3Config = new TechTransV3Config();
////        techTransV3Config.setProgramName("APIEXTERNAL");
////        techTransV3Config.setDeviceId("01");
////        techTransV3Config.setActivationCode("66FA4D4DB5AB4922597E22738682AAA5");
////        techTransV3Config.setLocationCode("B1-1800001");
////        techTransV3Config.setUpProgramName("APIEXTERNAL");
////        techTransV3Config.setStoreCode("B1-1800001");
////        techTransV3Config.setTillId("01");
////        techTransV3Config.setCashier("cashierJ001");
////        techTransV3Config.setItemCode("B1-1800001034034");
////        techTransV3Config.setTenderCode("CH");
////        techTransV3Config.setBaseCurrencyCode("RMB");
//
////        techTransV3Client.send("https://poscenteruat.kerryprops.com.cn/posservice/rest/salestransaction/lite", techTransV3Config, payLoad);
////
        AliPayTradeConfig aliPayTradeConfig = new AliPayTradeConfig();
        aliPayTradeConfig.setIsvAppId("2025012000000001");
        aliPayTradeConfig.setAppId("2021005116681195");
        aliPayTradeConfig.setPrivateKeyId("c88b6776-8187-4e53-9f97-018aba43505f");
        aliPayTradeConfig.setAlipayPublicKeyId("e6c13e1e-771a-4041-a255-e4e723b79426");
        aliPayTradeConfig.setFormat("json");
        aliPayTradeConfig.setCharset("UTF-8");
        aliPayTradeConfig.setSignType("RSA2");
        aliPayTradeClient.send("https://openapi.alipay.com/gateway.do", payLoad);

    }

}
