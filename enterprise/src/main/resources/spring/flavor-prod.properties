jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.connection.eviction.interval=60000

#enterprise
jdbc.enterprise.url=pk-enterprise-enterprise-2368

#db config
db.maxActive=200
db.maxIdle=200
db.minIdle=20

#kafka config
kafaka.topic=events.upay.trade,events.upay.charging
kafaka.brokers=172.16.10.85:9092,172.16.10.86:9092,172.16.11.237:9092,172.16.11.238:9092,172.16.12.175:9092
kafaka.concurrency=100
kafaka.registryUrl=http://172.16.10.85:8081,http://172.16.10.86:8081,http://172.16.11.237:8081,http://172.16.11.238:8081,http://172.16.12.175:8081
kafaka.groupId=trade.group.enterprise
kafka.autoCommitInterval=1000

#databus
spring.kafka.bootstrap-servers=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
spring.kafka.topic.merchant.basic.allin=databus_CRM_canal-merchant
spring.kafka.consumer.group-id=enterprise
spring.kafka.consumer.enableAutoCommit=true
spring.kafka.consumer.maxPollRecords=5000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=io.confluent.kafka.serializers.KafkaAvroDeserializer
spring.kafka.properties.schema.registry.url=http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081


#rpc server
core-business.server=http://app-core-business
sales-system.server=http://sales-system-service
user.service=http://user-service
upay-transaction.server=http://upay-transaction-query.internal.shouqianba.com
iot-server=http://shouqianba-iot-service
upay-trade=http://upay-trade
#upay-prepaid-card
upay-prepaid-card.server=http://upay-prepaid-card
merchant-contract-access.server=http://merchant-contract-access
merchant-user-service.server=http://merchant-user-service
uc-token-service.server= http://uc-token-service

#huawei notify
huawei.ssl=true
huawei.appId=com.huawei.security.bi.datalake
huawei.host=apigw.huawei.com
huawei.serviceUri=/service/data/v1/CASH_COLLECTION_BAR/execute

#alipay notify
alipay.isvAppId=2025012000000001
alipay.appId=2021005116681195
alipay.privateKeyId=c88b6776-8187-4e53-9f97-018aba43505f
alipay.alipayPublicKeyId=e6c13e1e-771a-4041-a255-e4e723b79426


#hope edu notify params
hopeedu.channel_code=16852465
hopeedu.access_token=daae6433dee94bf091a8cf2afbca7713


#Tracing
spring.application.name=enterprise
spring.application.env=prod
spring.application.rate=1.0f

logback.rootAppender=FT_CONSOLE_JSON

#redis
redis.url=r-bp1pveni08zbz66jld.redis.rds.aliyuncs.com
redis.port=6379
redis.database=16
redis.password=CMCIQ0w3EwRa09SE

executor.concurrency=100


lark.url=https://open.feishu.cn/open-apis/bot/v2/hook/144934b0-20b4-4eea-a89b-4a21e3f0ac50

