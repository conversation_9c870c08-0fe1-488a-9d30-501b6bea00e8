<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:vault-datasource-manager.xml"/>

    <bean id="databaseVault" class="com.wosai.middleware.vault.Vault" factory-method="autoload"/>

    <bean id="dataSourceTranslateImpls"
          class="com.wosai.database.instrumentation.spring.translate.impl.DBCP2DataSourceTranslate">
        <constructor-arg ref="dataSourceManager"/>
    </bean>

    <bean id="enterpriseDataSource"
          class="org.apache.commons.dbcp2.BasicDataSource"
          destroy-method="close"
          p:minIdle="${db.minIdle}"
          p:maxIdle="${db.maxIdle}"
          p:maxTotal="${db.maxActive}"
          p:driverClassName="${jdbc.driverClassName}"
          p:url="${jdbc.enterprise.url}"
          p:username="${jdbc.enterprise.username:}"
          p:password="${jdbc.enterprise.password:}"
          p:timeBetweenEvictionRunsMillis="${jdbc.connection.eviction.interval}"/>
</beans>
