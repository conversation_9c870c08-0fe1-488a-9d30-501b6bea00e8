jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.connection.eviction.interval=60000

#enterprise
jdbc.enterprise.url=pk-enterprise-enterprise-3865

#db config
db.maxActive=5
db.minIdle=1

#kafka config
kafaka.topic=events.upay.trade,events.upay.charging
kafaka.brokers=alikafka-post-public-intl-sg-d1s3ykl6t01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-d1s3ykl6t01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-d1s3ykl6t01-3-vpc.alikafka.aliyuncs.com:9092
kafaka.concurrency=100
kafaka.registryUrl=http://spg-schema-01.vpc.litnow.vn:8081,http://spg-schema-02.vpc.litnow.vn:8081,http://spg-schema-03.vpc.litnow.vn:8082
kafaka.groupId=trade.group.enterprise
kafka.autoCommitInterval=1000

#databus
spring.kafka.bootstrap-servers=alikafka-post-public-intl-sg-d1s3ykl6t01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-d1s3ykl6t01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-d1s3ykl6t01-3-vpc.alikafka.aliyuncs.com:9092
spring.kafka.topic.merchant.basic.allin=databus.event.merchant.basic.allin
spring.kafka.consumer.group-id=enterprise
spring.kafka.consumer.enableAutoCommit=true
spring.kafka.consumer.maxPollRecords=5000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=io.confluent.kafka.serializers.KafkaAvroDeserializer
spring.kafka.properties.schema.registry.url=http://spg-schema-01.vpc.litnow.vn:8081,http://spg-schema-02.vpc.litnow.vn:8081,http://spg-schema-03.vpc.litnow.vn:8082


#rpc server
core-business.server=http://core-business
sales-system.server=http://sales-system-service
user.service=http://user-service
upay-transaction.server=http://upay-transaction
iot-server=http://shouqianba-iot-service
upay-trade=http://upay-trade
#upay-prepaid-card
upay-prepaid-card.server=http://upay-prepaid-card
merchant-contract-access.server=http://merchant-contract-access

#huawei notify
huawei.ssl=true
huawei.appId=com.huawei.security.bi.datalake
huawei.appKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALPDlQzM7uQAVFlo2RW5eYHv8R6CJ1PgeW3ELMpY+SeCAETEv1IU0E1L655NdM7AdmVVfuKRChAblZVAS9Ywpx6NNUSLxH7et7GCs0v47qKiAbOE/3t1veC3nOMSRnL2tRqKElyDC0w3Xt0JfKEtXeTHKkwtehaDG4nX9MKxuWehAgMBAAECgYBFe+/9BqnAT7BK7xAaKZsh8pEM7dotvbKyDwYRINsNvl5ENpiNLaZmelYU1nO0Bwi8mpN1GAL/vunOyw+FN0EhFCxfmX4bvG1LT5pCg0NF1PZ0jmtpu3wX9S5Y4NxrWq00Wq0lRfsMRDAd4DJK7FBZpiyN7kF60ZJFmOzaMX6fEQJBANrQdvIOUNd83oxCc3nEOIgofLxeaMWahHPeZ05q10HT1V3SMT/TRd/aHviNfj+IPJKE5ldzSCC1aYQlFClZDwMCQQDSUDrix4yiHvmjegAYmBJMqFO3xeWgmOBQdD7lUKvs5p2pD/HUfxs/lYmiVaiVCD891x4EHW6r/58aQ6ovCGuLAkEA10q5XZxcSklgJpzTRYGdJcrAQA6cA6DupZq0BmH0/Z3Rl4hSULwtSf0w79k32IbRIlCE2pJg613TPePFnJE1xQJBAKJY6HvzCm6DdrVpEfXqPgYK0kVEcJPeLxosAflGotDG3OGKu2ULkV8t8gbsFKympxr3meYXSj1fn9PIc/DGkS0CQCg/FkE4kdyOY6qCLtbYSgqgM0Jhuqc64PlOjBGHbIzEJFhKjWTQ//WnHY0yZ5Ip4lEtUv/xctLM+FeurTAzFOo=
huawei.host=apigw.huawei.com
huawei.serviceUri=/service/data/v1/CASH_COLLECTION_BAR/execute


#Tracing
spring.application.name=enterprise
spring.application.env=prod
spring.application.rate=1.0f

logback.rootAppender=FT_CONSOLE_JSON

#redis
redis.url=r-zf8jn6zwvmi56zbtr4.redis.kualalumpur.rds.aliyuncs.com
redis.port=6379
redis.database=16
redis.password=Z#CwypafE@zVVJh!nguk6*5Q$SjZ

executor.concurrency=100


