<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="datasource-config.xml"/>

    <bean id="enterpriseJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="enterpriseDataSource"/>
    </bean>


    <!-- 配置事务管理器 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="enterpriseDataSource"/>
    </bean>

    <!-- 配置 TransactionTemplate -->
    <bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="transactionManager"/>
    </bean>




    <bean id="notifyMessageDao" class="com.wosai.data.dao.jdbc.JdbcDaoBase">
        <constructor-arg index="0" value="notify_message"/>
        <constructor-arg index="1" ref="enterpriseJdbcTemplate"/>
    </bean>
</beans>