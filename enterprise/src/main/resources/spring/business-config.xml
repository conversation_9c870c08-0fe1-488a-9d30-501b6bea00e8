<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:default}.properties"/>

    <context:annotation-config/>
    <context:component-scan base-package="com.wosai.bsm.enterprise"/>

    <import resource="jdbc-dao-config.xml"/>
    <import resource="tools-config.xml"/>
    <import resource="redis-config.xml" />

    <bean class="com.fasterxml.jackson.databind.ObjectMapper"/>
    <bean class="com.wosai.bsm.enterprise.util.ApolloUtil"/>

    <util:map id="kafkaConsumerConfig" key-type="java.lang.String" value-type="java.lang.Object" map-class="java.util.HashMap">
        <entry key="bootstrap.servers" value="${spring.kafka.bootstrap-servers}"/>
        <entry key="group.id" value="${spring.kafka.consumer.group-id}"/>
        <entry key="enable.auto.commit" value="${spring.kafka.consumer.enableAutoCommit}"/>
        <entry key="schema.registry.url" value="${spring.kafka.properties.schema.registry.url}"/>
        <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
        <entry key="value.deserializer" value="io.confluent.kafka.serializers.KafkaAvroDeserializer"/>
    </util:map>


    <bean class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping"/>
    <bean id="errorResolver" class="com.wosai.bsm.enterprise.helper.ExceptionBaseErrorResolver"/>

    <bean class="com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter">
        <property name="errorResolver" ref="errorResolver"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}/rpc/rsaKey"/>
        <property name="serviceInterface" value="com.wosai.upay.core.service.RsaKeyService"/>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}/rpc/merchant"/>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"/>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-trade}/rpc/trade"/>
        <property name="serviceInterface" value=" com.wosai.upay.trade.api.UpayTradeService"/>
        <property name="serverName" value="upay-trade"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>
    
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
	    <property name="serviceUrl" value="${iot-server}/rpc/deviceQrcode"/>
	    <property name="serviceInterface" value="com.wosai.iot.api.service.DeviceQrcodeService"/>
	    <property name="serverName" value="iot-service"/>
	    <property name="connectionTimeoutMillis" value="500"/>
	    <property name="readTimeoutMillis" value="4000"/>
	  </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${user.service}/rpc/group"/>
        <property name="serviceInterface" value="com.wosai.upay.user.api.service.GroupService"/>
        <property name="serverName" value="user-service"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="20000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}/rpc/terminal"/>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"/>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}/rpc/common"/>
        <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"/>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>



    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}/rpc/support"/>
        <property name="serviceInterface" value="com.wosai.upay.core.service.SupportService"/>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.wosai.mpay.api.zjtlcb.TLCBTokenCache">
    </bean>


    <bean id="zjtlcbClient" class="com.wosai.mpay.api.zjtlcb.ZJTLCBClient"></bean>

    <bean id="hopeEduClient" class="com.wosai.mpay.api.hopeedu.HopeEduClient"></bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${sales-system.server}/rpc/merchant"/>
        <property name="serviceInterface" value="com.wosai.sales.core.service.IMerchantService"/>
        <property name="serverName" value="sales-system"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>
    
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${sales-system.server}/rpc/organization"/>
        <property name="serviceInterface" value="com.wosai.sales.core.service.OrganizationService"/>
        <property name="serverName" value="sales-system"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>
    
    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-transaction.server}/rpc/order"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.OrderService"/>
        <property name="serverName" value="upay-transaction"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-transaction.server}/rpc/transaction_v2"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.TransactionServiceV2"/>
        <property name="serverName" value="upay-transaction"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}/rpc/member"/>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidMemberService"/>
        <property name="serverName" value="upay-prepaid-card"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-contract-access.server}/rpc/merchantProviderParams"/>
        <property name="serviceInterface" value="com.shouqianba.service.MerchantProviderParamsService"/>
        <property name="serverName" value="merchant-contract-access"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}/rpc/merchantuserV2"/>
        <property name="serviceInterface" value="com.wosai.app.service.v2.MerchantUserServiceV2"/>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>

    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${uc-token-service.server}/rpc/uc_token"/>
        <property name="serviceInterface" value="com.wosai.uc.service.UcTokenService"/>
        <property name="serverName" value="uc-token-service"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${uc-token-service.server}/rpc/uc_token_inner"/>
        <property name="serviceInterface" value="com.wosai.uc.service.UcTokenInnerService"/>
        <property name="serverName" value="uc-token-service"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="3000"/>
    </bean>



    <bean class="com.wosai.bsm.enterprise.consumer.TradeEventConsumer">
        <property name="topics" value="${kafaka.topic}"></property>
        <property name="brokers" value="${kafaka.brokers}"></property>
        <property name="concurrency" value="${kafaka.concurrency}"></property>
        <property name="registryUrl" value="${kafaka.registryUrl}"></property>
        <property name="groupId" value="${kafaka.groupId}"></property>
        <property name="autoCommitInterval" value="${kafka.autoCommitInterval}"></property>
    </bean>

    <bean id="threadPool" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="${executor.concurrency}"/>
    </bean>
    
    
    <task:annotation-driven/>
</beans>
