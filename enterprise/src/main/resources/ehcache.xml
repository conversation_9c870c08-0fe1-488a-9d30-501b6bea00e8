<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="false">
    <diskStore path="java.io.tmpdir"/>

    <cache name="NotifyConfig"
           timeToLiveSeconds="600"
           overflowToDisk="false"
           maxBytesLocalHeap="50m"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="5"
           memoryStoreEvictionPolicy="LRU"/>



    <cache name="RsaKeyData"
           timeToLiveSeconds="600"
           overflowToDisk="false"
           maxBytesLocalHeap="50m"
           maxElementsOnDisk="10000000"
           diskPersistent="false"
           diskExpiryThreadIntervalSeconds="5"
           memoryStoreEvictionPolicy="LRU"/>
</ehcache>
