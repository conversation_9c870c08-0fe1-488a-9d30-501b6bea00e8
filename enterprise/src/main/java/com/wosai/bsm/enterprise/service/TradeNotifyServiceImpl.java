package com.wosai.bsm.enterprise.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.biz.PrepaidBusinessBiz;
import com.wosai.bsm.enterprise.biz.TradeNotifyBiz;
import com.wosai.bsm.enterprise.biz.TransactionBiz;
import com.wosai.bsm.enterprise.client.alipay.AliPayTradeClient;
import com.wosai.bsm.enterprise.client.crland.CrLandClient;
import com.wosai.bsm.enterprise.client.crland.CrLandConfig;
import com.wosai.bsm.enterprise.client.hdcre.HDCREClient;
import com.wosai.bsm.enterprise.client.hdcre.HDCREConfig;
import com.wosai.bsm.enterprise.client.hopeedu.HopeEduMessageClient;
import com.wosai.bsm.enterprise.client.huawei.ApiGatewayClient;
import com.wosai.bsm.enterprise.client.huawei.ApiRequest;
import com.wosai.bsm.enterprise.client.huawei.ApiResponse;
import com.wosai.bsm.enterprise.client.huawei.Http;
import com.wosai.bsm.enterprise.client.icd.ICDClient;
import com.wosai.bsm.enterprise.client.icd.ICDConfig;
import com.wosai.bsm.enterprise.client.techtrans.TechTransClient;
import com.wosai.bsm.enterprise.client.techtrans.TechTransConfig;
import com.wosai.bsm.enterprise.client.techtransV2.TechTransV2Client;
import com.wosai.bsm.enterprise.client.techtransV2.TechTransV2Config;
import com.wosai.bsm.enterprise.client.techtransV3.TechTransV3Client;
import com.wosai.bsm.enterprise.client.techtransV3.TechTransV3Config;
import com.wosai.bsm.enterprise.client.zjtlcb.TlcbClient;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.model.Order;
import com.wosai.bsm.enterprise.model.SensitiveProperties;
import com.wosai.bsm.enterprise.model.Transaction;
import com.wosai.bsm.enterprise.util.*;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.iot.api.consts.IotDeviceQrcode;
import com.wosai.iot.api.service.DeviceQrcodeService;
import com.wosai.middleware.hera.toolkit.metrics.ActiveMetrics;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.ActiveSpan;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.SafeSimpleDateFormat;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.trade.api.UpayTradeService;
import com.wosai.upay.trade.api.request.TradeOrderQueryRequest;
import com.wosai.upay.trade.api.result.TradeOrderQueryResult;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.TransactionServiceV2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.bsm.enterprise.biz.TransactionBiz.SQB_MCH_PAYMENT_TYPE_LIST;
import static com.wosai.bsm.enterprise.biz.TransactionBiz.SQB_PAYMENT_TYPE_LIST;
import static com.wosai.bsm.enterprise.util.Constants.CODE_NO_NEED_PUSH;
import static com.wosai.bsm.enterprise.util.Constants.CODE_REQ_FAILURE;


@Service
@AutoJsonRpcServiceImpl
public class TradeNotifyServiceImpl implements TradeNotifyService {

    private static final Logger logger = LoggerFactory.getLogger(TradeNotifyServiceImpl.class);

    private ApiGatewayClient huaweiClient = new ApiGatewayClient();
    private static SafeSimpleDateFormat safeSimpleDateFormat = new SafeSimpleDateFormat("yyyy/MM/dd HH:mm:ss");


    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private TradeNotifyBiz tradeNotifyBiz;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private TradeFirstNotifyService firstNotifyService;
    @Autowired
    private DeviceQrcodeService deviceQrcodeService;

    @Autowired
    private ExternalServiceFacade externalServiceFacade;
    @Resource
    private PrepaidBusinessBiz prepaidBusinessBiz;

    @Autowired
    private BusinssCommonService businssCommonService;


    @Autowired
    private SnTicket snTicket;

    @Autowired
    private SensitiveProperties sensitiveProperties;

    // 华为通知配置
    @Value(value = "${huawei.ssl}")
    private boolean huaweiIsSSL;
    @Value(value = "${huawei.appId}")
    private String huaweiAppId;
    @Value(value = "${huawei.host}")
    private String huaweiHost;
    @Value(value = "${huawei.serviceUri}")
    private String huaweiServiceUri;


    // 支付宝通知配置
    @Value(value = "${alipay.isvAppId}")
    private String isvAppId;

    @Value(value = "${alipay.appId}")
    private String appId;

    @Value(value = "${alipay.privateKeyId}")
    private String privateKeyId;

    @Value(value = "${alipay.alipayPublicKeyId}")
    private String alipayPublicKeyId;


    public static String FIRST_SEND = "1";
    public static String RE_SEND = "2";

    @Autowired
    private UpayTradeService upayTradeService;

    @Autowired
    private TransactionServiceV2 transactionServiceV2;

    //获取notify_message 重试次数
    private int retryTime = 3;

    @Autowired
    private TlcbClient zjtlcbClient;

    @Autowired
    private ICDClient icdClient;

    @Autowired
    private HopeEduMessageClient hopeEduMessageClient;


    @Autowired
    private CrLandClient crLandClient;

    @Autowired
    private TechTransClient techTransClient;


    @Autowired
    private HDCREClient HDCREClient;

    @Autowired
    private TechTransV2Client techTransV2Client;


    @Autowired
    private TechTransV3Client techTransV3Client;

    @Autowired
    private AliPayTradeClient aliPayTradeClient;



    private Set<String> excludeKeys = CollectionUtil.hashSet(DaoConstants.ID, NotifyMessage.OBJECT_ID, NotifyMessage.PUSH_DATE,
            NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_RESP, NotifyMessage.PUSH_TIME, NotifyMessage.PUSH_COUNT,
            DaoConstants.VERSION, NotifyMessage.STORE_CLIENT_SN, NotifyMessage.STORE_NAME, NotifyMessage.TERMINAL_NAME);
    private Set<String> huaweiIncludeKeys = CollectionUtil.hashSet(NotifyMessage.STORE_CLIENT_SN, NotifyMessage.STORE_NAME, NotifyMessage.TERMINAL_NAME);
    
    private static final Map<String, Set<String>> LOCAL_GROUP_MERCHANT_CACHE = new ConcurrentHashMap<String, Set<String>>();
    private LoadingCache<String, Map<String, Object>> localTerminalCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(1, TimeUnit.HOURS).build(new CacheLoader<String, Map<String, Object>>() {
                @Override
                public Map<String, Object> load(String key) throws Exception {
                    return getTerminalById(key);
                }
            });

    private LoadingCache<String, String> localDeviceSnCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(1, TimeUnit.HOURS).build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    return getDeviceSnByDeviceFingerprint(key);
                }
            });

    @Trace
    @Timed("consumer")
    public void notifyTrade(Map<String, Object> transaction) throws EnterpriseException {
        List<Map<String, Object>> notifyConfigList = getNotifyConfig(transaction);
        notifyConfigList.forEach(notifyConfig -> {
            try {
                notifyTrade(transaction, FIRST_SEND, notifyConfig);
            } catch (Exception e) {
                String notifyUrl = MapUtil.getString(notifyConfig, NotifyMessage.NOTIFY_URL);
                logger.error("第{}次推送数据失败, notifyUrl={}, 对应的流水信息={}", FIRST_SEND, notifyUrl, JSONObject.toJSONString(transaction), e);
            }

        });
    }


    public void notifyTrade(Map<String, Object> transaction, String sendType, Map<String, Object> notifyConfig) throws EnterpriseException {
        //校验
        boolean valid = valid(transaction, notifyConfig);
        if (!valid) {
            return;
        }
        try {
            final Map<String, Object> payload = new HashMap<String, Object>();
            //初始化payload
            initPayload(payload, notifyConfig, transaction);
            String[] urls = MapUtil.getString(notifyConfig, NotifyConfig.NOTIFY_URL).split(",");
            //初始化id
            for (int i = 0; i < urls.length; i++) {
                String currentNotifyUrl = urls[i];
                long id = snTicket.nextSn(retryTime);
                HashMap<String, Object> newPayload = new HashMap<>(payload);
                newPayload.put(DaoConstants.ID, id);
                newPayload.put(NotifyMessage.NOTIFY_URL, currentNotifyUrl);
                if (sendType.equals(FIRST_SEND)) {
                    tradeNotifyBiz.addInitNotifyMessage(notifyConfig, newPayload);
                    firstNotifyService.push(notifyConfig, newPayload);
                } else if (sendType.equals(RE_SEND)) {
                    //入库，重推任务自动重推
                    tradeNotifyBiz.addInitNotifyMessage(notifyConfig, newPayload, RE_SEND);
                }
            }

        } catch (Exception e) {
            if (!(e instanceof EnterpriseException)) {
                ActiveMetrics.error(e);
                logger.warn("error in notifyTrade", e);
            }
            throw e;
        }

    }


    private List<Map<String, Object>> getNotifyConfig(Map<String, Object> transaction) {
        List<Map<String, Object>> notifyConfigList = new ArrayList<>();
        final String merchantId = MapUtils.getString(transaction, Transaction.MERCHANT_ID);
        Map<String, Object> notifyConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_MERCHANT,
                merchantId);

        if (MapUtils.isEmpty(notifyConfig)) {
            for (String groupId : LOCAL_GROUP_MERCHANT_CACHE.keySet()) {
                if (LOCAL_GROUP_MERCHANT_CACHE.get(groupId).contains(merchantId)) {
                    notifyConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_GROUP, groupId);
                    break;
                }
            }
        }
        if (MapUtils.isEmpty(notifyConfig)) {
            String vendorId = BeanUtil.getPropString(transaction, "config_snapshot.vendor_id");
            if (null != vendorId) {
                notifyConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_VENDOR, vendorId);
            }
        }
        if (notifyConfig != null) {
            notifyConfigList.add(notifyConfig);
        }
        // 获得店铺配置
        final String storeId = MapUtils.getString(transaction, Transaction.STORE_ID);
        Map<String, Object> notifyStoreConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_STORE,
                storeId);
        if (notifyStoreConfig != null) {
            notifyConfigList.add(notifyStoreConfig);
        }

        // 获取支付宝推送配置
        Map<String, Object> notifyAliPayConfig = tradeNotifyBiz.queryNotifyConfig(NotifyConfig.OBJECT_TYPE_ALIPAY_SERVICE, merchantId);
        if (notifyAliPayConfig != null) {
            notifyConfigList.add(notifyAliPayConfig);
        }
        return notifyConfigList;
    }

    private boolean valid(Map<String, Object> transaction, Map<String, Object> notifyConfig) {
        final String merchantId = MapUtils.getString(transaction, Transaction.MERCHANT_ID);
        // 未配置推送
        if (MapUtils.isEmpty(notifyConfig))
            return false;
        int type = BeanUtil.getPropInt(notifyConfig, NotifyConfig.NOTIFY_TYPE);
        // 推送类型小于0的情况不推送
        if (type < 0) {
            logger.info("no need push for merchant {}", merchantId);
            return false;
        }

        //配置支持上送流水的类型 ： 交易流水 、 非交易流水
        int intPayway = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
        Integer notifyPaywayType = MapUtil.getInteger(notifyConfig, NotifyConfig.NOTIFY_PAYWAY_TYPE, NotifyConfig.NOTIFY_PAYWAY_TYPE_NON_CHARGE);

        if(externalServiceFacade.isChargePayway(intPayway)) {
            if(!Objects.equals(notifyPaywayType, NotifyConfig.NOTIFY_PAYWAY_TYPE_CHARGE) && !Objects.equals(notifyPaywayType, NotifyConfig.NOTIFY_PAYWAY_TYPE_ALL)) {
                return  false;
            }

        } else {
            //交易类流水
            if(!Objects.equals(notifyPaywayType, NotifyConfig.NOTIFY_PAYWAY_TYPE_NON_CHARGE) && !Objects.equals(notifyPaywayType, NotifyConfig.NOTIFY_PAYWAY_TYPE_ALL)) {
                return  false;
            }

        }


        if (ApolloUtil.getPaywayLimit()) {
            String payway = Order.PAYWAY_MAP_V2.get(intPayway);
            String notifyGateway = MapUtils.getString(notifyConfig, NotifyConfig.NOTIFY_GATEWAY);
            // 配置了指定推送的支付方式
            if (StringUtils.isNotBlank(notifyGateway) && !StringUtils.contains(notifyGateway, payway)) {
                logger.info("no need push for merchant {}", merchantId);
                return false;
            }
        }
        return true;
    }


    void initPayload(Map<String, Object> payload, Map<String, Object> notifyConfig, Map<String, Object> transaction) {
        final String merchantId = MapUtils.getString(transaction, Transaction.MERCHANT_ID);
        int type = BeanUtil.getPropInt(notifyConfig, NotifyConfig.NOTIFY_TYPE);
        payload.put(NotifyMessage.SN, MapUtils.getString(transaction, Transaction.ORDER_SN));
        payload.put(NotifyMessage.BIZ_ORDER_SN, TransactionBiz.getSqbBizOrderSn(transaction));
        payload.put(NotifyMessage.TSN, MapUtils.getString(transaction, Transaction.TSN));
        payload.put(NotifyMessage.TYPE, TransactionBiz.getTransactionType(transaction));
        payload.put(NotifyMessage.IS_STORED_IN, TransactionBiz.isStoredIn(transaction));
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(NotifyMessage.IS_STORED_PAY, TransactionBiz.isStoredPay(transaction));
        payload.put(NotifyMessage.EXTRA_DATA, extraMap);
        payload.put(NotifyMessage.SQB_PLATFORM_DISCOUNT_AMOUNT, TransactionBiz.getDiscountAmount(transaction, SQB_PAYMENT_TYPE_LIST));
        payload.put(NotifyMessage.SQB_MCH_DISCOUNT_AMOUNT, TransactionBiz.getDiscountAmount(transaction, SQB_MCH_PAYMENT_TYPE_LIST));
        //添加储值相关字段
        prepaidBusinessBiz.addPrepaidCardFieldToPayload(payload, transaction, notifyConfig);

        String clientSn = MapUtils.getString(transaction, Transaction.CLIENT_TSN);
        payload.put(NotifyMessage.CLIENT_TSN, clientSn);
        payload.put(NotifyMessage.STATUS, "SUCCESS");
        payload.put(NotifyMessage.PAYWAY, MapUtils.getString(transaction, Transaction.PAYWAY));
        payload.put(NotifyMessage.SUB_PAYWAY, MapUtils.getString(transaction, Transaction.SUB_PAYWAY));
        payload.put(NotifyMessage.PAYER_UID, MapUtils.getString(transaction, Transaction.BUYER_UID, ""));
        payload.put(NotifyMessage.FINISH_TIME, MapUtils.getString(transaction, Transaction.FINISH_TIME));
        // 撤单时kafka中的CHANNEL_FINISH_TIME=0
        String channelFinishTime = MapUtils.getString(transaction, Transaction.CHANNEL_FINISH_TIME);
        if("0".equals(channelFinishTime)) {
            channelFinishTime = null;
        }
        payload.put(NotifyMessage.CHANNEL_FINISH_TIME, channelFinishTime);
        payload.put(NotifyMessage.STORE_ID, MapUtils.getString(transaction, Transaction.STORE_ID));
        payload.put(NotifyMessage.TERMINAL_ID, MapUtils.getString(transaction, Transaction.TERMINAL_ID, ""));

        //计算结算金额并写入Map
        payload.put(NotifyMessage.SETTLEMENT_AMOUNT, PaymentUtil.calculateSettlementAmountByTransaction(transaction));
        payload.put(NotifyMessage.AMOUNT, MapUtils.getString(transaction, Transaction.ORIGINAL_AMOUNT));

        int transactionType = MapUtils.getIntValue(transaction, Transaction.TYPE);
        if(TransactionBiz.isPayTransaction(transaction)) {
            String orderStatus = Transaction.TYPE_DEPOSIT_FREEZE == transactionType ? Order.Status.DEPOSIT_FREEZED.toString() :Order.Status.PAID.toString();
            payload.put(NotifyMessage.CLIENT_SN, MapUtils.getString(transaction, Transaction.CLIENT_TSN));
            payload.put(NotifyMessage.ORDER_STATUS, orderStatus);
            payload.put(NotifyMessage.CTIME, MapUtils.getString(transaction, DaoConstants.CTIME));
            payload.put(NotifyMessage.SUBJECT, MapUtils.getString(transaction, Transaction.SUBJECT));
            payload.put(NotifyMessage.TRADE_NO, MapUtils.getString(transaction, Transaction.TRADE_NO, ""));
            payload.put(NotifyMessage.TOTAL_AMOUNT, MapUtils.getString(transaction, Transaction.ORIGINAL_AMOUNT));
            payload.put(NotifyMessage.NET_AMOUNT, MapUtils.getString(transaction, Transaction.ORIGINAL_AMOUNT));
            payload.put(NotifyMessage.OPERATOR, MapUtils.getString(transaction, Transaction.OPERATOR, ""));
            payload.put(NotifyMessage.REFLECT, transaction.get(Transaction.REFLECT));

        } else {
            Map order = MapUtil.getMap((Map)transaction.get(Transaction.EXTRA_OUT_FIELDS), Transaction.ORDER_INFO);
            if(null == order) {
                order = orderService.getOrderByMerchantIdAndOrderSn(merchantId, BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
            }
            payload.put(NotifyMessage.CLIENT_SN, MapUtils.getString(order, Order.CLIENT_SN));
            payload.put(NotifyMessage.ORDER_STATUS,
                    Order.Status.fromCode(MapUtils.getIntValue(order, Order.STATUS)).toString());
            payload.put(NotifyMessage.CTIME, MapUtils.getString(order, DaoConstants.CTIME));
            payload.put(NotifyMessage.SUBJECT, MapUtils.getString(order, Order.SUBJECT));
            payload.put(NotifyMessage.TRADE_NO, MapUtils.getString(order, Order.TRADE_NO, ""));
            payload.put(NotifyMessage.TOTAL_AMOUNT, MapUtils.getString(order, Order.ORIGINAL_TOTAL));
            payload.put(NotifyMessage.NET_AMOUNT, MapUtils.getString(order, Order.NET_ORIGINAL));
            payload.put(NotifyMessage.OPERATOR, MapUtils.getString(order, Order.OPERATOR, ""));
            payload.put(NotifyMessage.REFLECT, MapUtils.getObject(order, Order.REFLECT));
        }

        //通用收款单 新增字段
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG);
        if (!Objects.isNull(productFlag) && productFlag.contains(ProductFlag.FORMPAY_GENERAL.getCode())) {
            if (!Objects.isNull(clientSn)) {
                TradeOrderQueryRequest tradeOrderQueryRequest = new TradeOrderQueryRequest();
                tradeOrderQueryRequest.setOrderSn(clientSn);
                tradeOrderQueryRequest.setMerchantId(merchantId);
                TradeOrderQueryResult tradeOrderQueryResult = upayTradeService.queryOrder(tradeOrderQueryRequest);
                Object bizExt = tradeOrderQueryResult.getBizExt();
                if (!Objects.isNull(bizExt)) {
                    Map bizExtMap = (Map) bizExt;
                    Map formMap = new HashMap();
                    formMap.put("formCode", MapUtil.getString(bizExtMap, "formCode"));
                    formMap.put("formId", MapUtil.getString(bizExtMap, "formId"));
                    formMap.put("customField", MapUtil.getString(bizExtMap, "customField"));
                    payload.put(NotifyMessage.FORM_BIZ_EXT, formMap);
                }
            }
        }

        String notifyStatus = MapUtils.getString(notifyConfig, NotifyConfig.NOTIFY_STATUS);
        String payloadStatus = MapUtils.getString(payload, NotifyMessage.ORDER_STATUS);
        // 配置了指定推送的订单状态
        if (StringUtils.isNotBlank(notifyStatus) && !StringUtils.contains(notifyStatus, payloadStatus)) {
            throw new EnterpriseException(CODE_NO_NEED_PUSH, "no need push for status " + payloadStatus);
        }

        // 根据不同推送类型增加推送的字段
        switch (type) {
            // 额外推送三个字段, merchant_sn, terminal_sn, device_fingerprint
            case NotifyConfig.NOTIFY_TYPE_CUSTOMIZED:
            case NotifyConfig.NOTIFY_TYPE_HUAWEI:
                Map<String,Object> configSnaphot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
                String terminalId = MapUtils.getString(transaction, Transaction.TERMINAL_ID);
                Map<String, Object> terminal = null;
                if (StringUtils.isNotEmpty(terminalId)) {
                    try {
                        terminal = localTerminalCache.get(terminalId);
                    } catch (Exception e) {
                        logger.info("get terminal info error: " + payload, e);
                    }
                }
                String vendorAppAppId = MapUtils.getString(terminal, Terminal.VENDOR_APP_APPID);
                String deviceFingerprint = MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT);
                if(!MapUtil.isEmpty(configSnaphot)){
                    payload.put(NotifyMessage.STORE_SN, MapUtils.getString(configSnaphot, TransactionParam.STORE_SN));
                    payload.put(NotifyMessage.MERCHANT_SN, MapUtils.getString(configSnaphot, TransactionParam.MERCHANT_SN));
                    payload.put(NotifyMessage.TERMINAL_SN, MapUtils.getString(configSnaphot, TransactionParam.TERMINAL_SN));
                } else {
                    payload.put(NotifyMessage.STORE_SN, MapUtils.getString(transaction, TransactionParam.STORE_SN));
                    payload.put(NotifyMessage.MERCHANT_SN, MapUtils.getString(transaction, TransactionParam.MERCHANT_SN));
                    payload.put(NotifyMessage.TERMINAL_SN, MapUtils.getString(transaction, TransactionParam.TERMINAL_SN));
                }

                payload.put(NotifyMessage.DEVICE_FINGERPRINT, deviceFingerprint);
                String deviceSn = getDeviceSn(deviceFingerprint, vendorAppAppId);
                if(!StringUtil.empty(deviceSn)) {
                    payload.put(NotifyMessage.DEVICE_SN, deviceSn);
                }
                if(type == NotifyConfig.NOTIFY_TYPE_HUAWEI) {
                    if(!MapUtil.isEmpty(configSnaphot)){
                        payload.put(NotifyMessage.STORE_CLIENT_SN, MapUtils.getString(configSnaphot, TransactionParam.STORE_CLIENT_SN));
                        payload.put(NotifyMessage.STORE_NAME, MapUtils.getString(configSnaphot, TransactionParam.STORE_NAME));
                        payload.put(NotifyMessage.TERMINAL_NAME, MapUtils.getString(configSnaphot, TransactionParam.TERMINAL_NAME));
                    } else {
                        payload.put(NotifyMessage.STORE_CLIENT_SN, MapUtils.getString(transaction, TransactionParam.STORE_CLIENT_SN));
                        payload.put(NotifyMessage.STORE_NAME, MapUtils.getString(transaction, TransactionParam.STORE_NAME));
                        payload.put(NotifyMessage.TERMINAL_NAME, MapUtils.getString(transaction, TransactionParam.TERMINAL_NAME));
                    }

                }
                break;
            default:
                break;
        }

        // 判断院校通推送增加属性
        String notifyUrl = MapUtils.getString(notifyConfig, NotifyConfig.NOTIFY_URL, "");
        if (ApolloUtil.getHopeEduPushUrls().contains(notifyUrl)) {
            // 院校通推送
            initHopeEduParams(payload, transaction);
        }

        logger.info("need push message {}", transaction);
    }

    /**
     * 初始化院校通属性
     * @param payload
     * @param transaction
     */
    private void initHopeEduParams(Map<String, Object> payload, Map<String, Object> transaction) {

        Map<String,Object> configSnaphot = (Map) transaction.get(Transaction.CONFIG_SNAPSHOT);
        if(!MapUtil.isEmpty(configSnaphot)){
            payload.put(NotifyMessage.MERCHANT_SN, MapUtils.getString(configSnaphot, TransactionParam.MERCHANT_SN));
            payload.put(NotifyMessage.TERMINAL_SN, MapUtils.getString(configSnaphot, TransactionParam.TERMINAL_SN));
            payload.put(NotifyMessage.MERCHANT_NAME, MapUtils.getString(configSnaphot, TransactionParam.MERCHANT_NAME));
            payload.put(NotifyMessage.TERM_ID, MapUtils.getString(configSnaphot, TransactionParam.TRADE_EXT_TERM_ID));

            for (String key : configSnaphot.keySet()) {
                if (key.contains("_trade_params")) {
                    Map<String, Object> tradeParams = MapUtils.getMap(configSnaphot, key);
                    payload.put(NotifyMessage.PROVIDER_MCH_ID, MapUtils.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID, ""));
                    break;
                }
            }
        } else {
            payload.put(NotifyMessage.MERCHANT_SN, MapUtils.getString(transaction, TransactionParam.MERCHANT_SN));
            payload.put(NotifyMessage.TERMINAL_SN, MapUtils.getString(transaction, TransactionParam.TERMINAL_SN));
            payload.put(NotifyMessage.MERCHANT_NAME, "");
            payload.put(NotifyMessage.TERM_ID, "");
            payload.put(NotifyMessage.PROVIDER_MCH_ID, "");
        }

        String terminalId = MapUtils.getString(transaction, Transaction.TERMINAL_ID);
        Map<String, Object> terminal = null;
        if (StringUtils.isNotEmpty(terminalId)) {
            try {
                terminal = localTerminalCache.get(terminalId);
            } catch (Exception e) {
                logger.info("get terminal info error: " + payload, e);
            }
        }
        String deviceFingerprint = MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT, "");
        payload.put(NotifyMessage.DEVICE_FINGERPRINT, deviceFingerprint);

        payload.put(NotifyMessage.ORDER_SN, MapUtils.getString(transaction, TransactionParam.ORDER_SN));
        payload.put(NotifyMessage.BUYER_UID, MapUtils.getString(transaction, TransactionParam.BUYER_UID));

        Map<String,Object> extraOutFields = (Map) transaction.get(Transaction.EXTRA_OUT_FIELDS);
        if (!MapUtil.isEmpty(extraOutFields)) {
            List<Map<String, Object>> payments = (List<Map<String, Object>>)extraOutFields.getOrDefault(TransactionParam.PAYMENTS, new ArrayList<>());

            if (Objects.isNull(payments) || payments.isEmpty()) {
                payload.put(NotifyMessage.ORIGIN_TYPE_LIST, "");
            } else {
                List<String> types = payments.stream()
                        .filter(Objects::nonNull)
                        .map(payment -> MapUtils.getString(payment, TransactionParam.ORIGIN_TYPE, ""))
                        .collect(Collectors.toList());

                payload.put(NotifyMessage.ORIGIN_TYPE_LIST, String.join(",", types));
            }
        } else {
            payload.put(NotifyMessage.ORIGIN_TYPE_LIST, "");
        }
    }

    /**
     * 针对华为移除无用的字段
     */
    private void removeUnUsedFieldForHuaWei(Map<String, Object> payload) {
        for (String key : NotifyMessage.EXTRA_KEY) {
            if(NotifyMessage.FORM_BIZ_EXT.equals(key)) {
                continue;
            }
            payload.remove(key);
        }
    }

    @Override
    public void notifyMessage(Map<String, Object> notifyMessage, Map<String, Object> notifyConfig)
            throws EnterpriseException {
        String notifyUrl = MapUtil.getString(notifyMessage, NotifyMessage.NOTIFY_URL);
        String appId = MapUtils.getString(notifyConfig,  NotifyConfig.APP_ID);
        String appSecret = MapUtils.getString(notifyConfig, NotifyConfig.APP_SECRET);
        String notifyResp = MapUtils.getString(notifyConfig, NotifyConfig.NOTIFY_RESP);
        StopWatch watch = new StopWatch();
        logger.info("push messages ：{} ", JSON.toJSONString(notifyMessage));
        watch.start();
        traceCall(notifyUrl, () ->{
            String exportType= MapUtils.getString(notifyMessage, NotifyMessage.TYPE);
            Long originalAmount = MapUtils.getLongValue(notifyMessage, NotifyMessage.TOTAL_AMOUNT, 0L);
            Long refundAmount = MapUtils.getLongValue(notifyMessage, NotifyMessage.AMOUNT, 0L);
            String netAmount = String.valueOf(originalAmount - refundAmount);
            String response = null;
            boolean isSuccess = false;
            ApiResponse apiResponse = null;
            try {
                Map<String, Object> payload = this.getPayload(notifyMessage, appId);
                if (ApolloUtil.getHuiWeiPushUrls().contains(notifyUrl)) {
                    //针对华为移除无用的字段
                    removeUnUsedFieldForHuaWei(payload);
                    // 添加华为推送字段
                    huaweiIncludeKeys.forEach(key ->{
                        payload.put(key, notifyMessage.get(key));
                    });
                    if (MessyCodeUtil.isMessyCode(MapUtils.getString(payload, NotifyMessage.OPERATOR))) {
                        logger.info("change operator, ordersn = {}, source = {}, target = 01",
                                MapUtils.getString(notifyMessage, NotifyMessage.SN),
                                MapUtils.getString(payload, NotifyMessage.OPERATOR));
                        payload.put(NotifyMessage.OPERATOR, "01");
                    }
                    JSONObject request = new JSONObject();
                    for (String key : payload.keySet()) {
                        if (NotifyMessage.NET_AMOUNT.equalsIgnoreCase(key)) {
                            if (!TransactionBiz.PAY_TRANSACTION_EXPORT_TYPE_LIST.contains(exportType)) {
                                request.put(key.toUpperCase(), netAmount);
                                continue;
                            }
                        }
                        request.put(key.toUpperCase(), payload.get(key));
                    }
                    request.put("LAST_MODIFY_DATE", safeSimpleDateFormat.format(new Date()));
                    logger.info("request {}-{} payload: {}", appId, MapUtils.getString(notifyMessage, NotifyMessage.SN),
                            request.toJSONString());

                    ApiRequest huaweiRequest = new ApiRequest(huaweiIsSSL,
                            huaweiHost);
                    huaweiRequest.setAppId(huaweiAppId);
                    huaweiRequest.setAppKey(sensitiveProperties.getHuaweiAppKey());
                    huaweiRequest.setServiceUri(huaweiServiceUri);
                    huaweiRequest.setMethod(Http.HTTP_POST);

                    /* 设置请求Header的内容 */
                    Map<String, String> headers = new HashMap<String, String>();
                    headers.put("X-DATA", "T1");
                    headers.put("X-NUM", "18");
                    headers.put("X-ENUM", "THIN");

                    huaweiRequest.setHeaders(headers);
                    huaweiRequest.setHeader("X-DATA", "T2");

                    huaweiRequest.setHeader("Authorization", getAuthorization(appId, appSecret, request.toJSONString()));
                    huaweiRequest.setParameters(new HashMap<String, String>() {
                        {
                            put("params", request.toJSONString());
                        }
                    });

                    huaweiRequest.setContent("Hello API-Gateway");

                    apiResponse = huaweiClient.execute(huaweiRequest);
                    if (apiResponse.getStatusCode() == Http.HTTP_OK) {
                        response = apiResponse.getContentString();
                        Map jsonResp = JSON.parseObject(response);
                        if ("200".equals(BeanUtil.getPropString(jsonResp, "status"))
                                || BeanUtil.getPropString(jsonResp, "message", "").contains("ORA-00001")) {
                            isSuccess = true;
                        }
                        ;
                        logger.info("response from {}-{}: {}", appId, MapUtils.getString(notifyMessage, NotifyMessage.SN),
                                null != response ? response.replaceAll("\\n", "") : "");
                    } else {
                        String errorMsg = "remote call fail";
                        if(null != apiResponse) {
                            errorMsg += String.format(", statusCode:%s, content: %s", apiResponse.getStatusCode(), apiResponse.getContentString());
                        }
                        throw new RuntimeException(errorMsg);
                    }
                } else if(ApolloUtil.getZJTLUrls().contains(notifyUrl)) {
                    Pair<String, Boolean> pair = zjtlcbClient.send(payload, notifyResp, notifyUrl);
                    response = pair.getLeft();
                    isSuccess = pair.getRight();

                } else if (ApolloUtil.getICDUrls().contains(notifyUrl)) {
                    // 得到配置信息
                    String extraConfig = (String)notifyConfig.get(NotifyConfig.EXTRA);
                    // 额外配置信息字节转化为字符串
                    if (StringUtils.isNotBlank(extraConfig)) {
                        ICDConfig icdConfig = JSON.parseObject(extraConfig, ICDConfig.class);
                        Pair<String, Boolean> pair = icdClient.send(notifyUrl, icdConfig, notifyMessage);
                        response = pair.getLeft();
                        isSuccess = pair.getRight();
                    } else {
                        response = "ICD配置信息不存在,不满足推送条件";
                        isSuccess = true;
                    }

                } else if (ApolloUtil.getCrLandUrls().contains(notifyUrl)) {
                    // 得到配置信息
                    String extraConfig = (String)notifyConfig.get(NotifyConfig.EXTRA);
                    // 额外配置信息字节转化为字符串
                    if (StringUtils.isNotBlank(extraConfig)) {
                        CrLandConfig crLandConfig = JSON.parseObject(extraConfig, CrLandConfig.class);
                        Pair<String, Boolean> pair = crLandClient.send(notifyUrl, crLandConfig, notifyMessage);
                        response = pair.getLeft();
                        isSuccess = pair.getRight();
                    } else {
                        response = "CrLand配置信息不存在,不满足推送条件";
                        isSuccess = true;
                    }
                } else if (ApolloUtil.getTechTransUrls().contains(notifyUrl)) {
                    // 得到配置信息
                    String extraConfig = "";
                    try {
                        extraConfig = (String) notifyConfig.get(NotifyConfig.EXTRA);
                    } catch (Exception e) {
                        logger.error("科传配置信息转化失败,配置为{}", notifyConfig, e);
                        throw new Exception("配置信息转化失败", e);
                    }
                    // 额外配置信息字节转化为字符串
                    if (StringUtils.isNotBlank(extraConfig)) {
                        TechTransConfig techTransConfig = JSON.parseObject(extraConfig, TechTransConfig.class);
                        Pair<String, Boolean> pair = techTransClient.send(notifyUrl, techTransConfig, notifyMessage);
                        response = pair.getLeft();
                        isSuccess = pair.getRight();
                    } else {
                        response = "TechTrans配置信息不存在,不满足推送条件";
                        isSuccess = true;
                    }
                } else if (ApolloUtil.getHdcreConfig().keySet().contains(notifyUrl)) {
                    // 得到配置信息
                    String extraConfig = (String) notifyConfig.get(NotifyConfig.EXTRA);
                    // 额外配置信息字节转化为字符串
                    if (StringUtils.isNotBlank(extraConfig)) {
                        HDCREConfig hdcreConfig = JSON.parseObject(extraConfig, HDCREConfig.class);
                        Pair<String, Boolean> pair = HDCREClient.send(notifyUrl, hdcreConfig, notifyMessage);
                        response = pair.getLeft();
                        isSuccess = pair.getRight();
                    } else {
                        response = "HDCRE配置信息不存在,不满足推送条件";
                        isSuccess = true;
                    }
                } else if (ApolloUtil.getTechTransV2Urls().contains(notifyUrl)){
                    // 得到配置信息
                    String extraConfig = (String) notifyConfig.get(NotifyConfig.EXTRA);
                    // 额外配置信息字节转化为字符串
                    if (StringUtils.isNotBlank(extraConfig)) {
                        TechTransV2Config techTransConfig = JSON.parseObject(extraConfig, TechTransV2Config.class);
                        Pair<String, Boolean> pair = techTransV2Client.send(notifyUrl, techTransConfig, notifyMessage);
                        response = pair.getLeft();
                        isSuccess = pair.getRight();
                    } else {
                        response = "TechTrans配置信息不存在,不满足推送条件";
                        isSuccess = true;
                    }
                } else if (ApolloUtil.getTechTransV3Urls().keySet().contains(notifyUrl)) {
                    // 得到配置信息
                    String extraConfig = (String) notifyConfig.get(NotifyConfig.EXTRA);
                    // 额外配置信息字节转化为字符串
                    if (StringUtils.isNotBlank(extraConfig)) {
                        TechTransV3Config techTransConfig = JSON.parseObject(extraConfig, TechTransV3Config.class);
                        Pair<String, Boolean> pair = techTransV3Client.send(notifyUrl, techTransConfig, notifyMessage);
                        response = pair.getLeft();
                        isSuccess = pair.getRight();
                    } else {
                        response = "TechTransV3配置信息不存在,不满足推送条件";
                        isSuccess = true;
                    }
                } else if (ApolloUtil.getAliPayMerchantTradeUrls().contains(notifyUrl)) {
                    Pair<String, Boolean> pair = aliPayTradeClient.send(notifyUrl, notifyMessage);
                    response = pair.getLeft();
                    isSuccess = pair.getRight();
                } else if (ApolloUtil.getHopeEduPushUrls().contains(notifyUrl)) {
                    Pair<String, Boolean> pair = hopeEduMessageClient.send(notifyUrl, notifyMessage, notifyConfig);
                    response = pair.getLeft();
                    isSuccess = pair.getRight();
                } else {
                    // 去除额外数据
                    if (Objects.nonNull(payload)) {
                        payload.remove(NotifyMessage.EXTRA_DATA);
                    }
                    final String body = objectMapper.writeValueAsString(payload);
                    logger.info("request {}-{} payload: {}", appId, MapUtils.getString(notifyMessage, NotifyMessage.SN),
                            body);

                    int readTimeOut = MapUtil.getIntValue(notifyMessage, NotifyMessage.PUSH_COUNT) == 0 ? 1000 : 5000;
                    // 请求回调接口
                    response = HttpClientUtils.doPost("enterprise", null, null, notifyUrl, "application/json", body, getAuthorizationHeader(appId, appSecret, body), "utf-8", 2000, readTimeOut);
                    // 如果不是配置好的成功回复,则认为是推送失败,需要重试
                    if (StringUtils.isNotBlank(notifyResp) && !notifyResp.equalsIgnoreCase(response)) {
                        throw new EnterpriseException(CODE_REQ_FAILURE, "request " + appId + "-"
                                + MapUtils.getString(notifyMessage, NotifyMessage.SN) + " not success");
                    }
                    isSuccess = true;
                    logger.info("response from {}-{}: {}", appId, MapUtils.getString(notifyMessage, NotifyMessage.SN),
                            response);
                }
            } catch (Exception e) {
                if (e instanceof MpayApiNetworkError && e.getCause() != null && e.getCause() instanceof Exception) {
                    e = new MpayApiNetworkError(e.getMessage() + ":" + e.getCause().getMessage());
                }
                logger.error(String.format("response from %s-%s fail, notifyUrl: %s, ex: %s", appId, MapUtils.getString(notifyMessage, NotifyMessage.SN), notifyUrl, e.getMessage()), e);
                if (!(e instanceof EnterpriseException)) {
                    ActiveSpan.error(e);
                }

                int pushCount = MapUtils.getIntValue(notifyMessage, NotifyMessage.PUSH_COUNT);
                throw new EnterpriseException(CODE_REQ_FAILURE, pushCount + " request " + appId + "-"
                        + MapUtils.getString(notifyMessage, NotifyMessage.SN) + " error: " + e.getMessage());
            } finally {
                watch.stop();
                GatewayAnalysis.put(MapUtil.getString(notifyConfig, NotifyConfig.NOTIFY_URL), watch.getTotalTimeMillis());
                logger.info("request appId-{} orderSn-{} cost-{} ms", appId, MapUtils.getString(notifyMessage, NotifyMessage.SN), watch.getTotalTimeMillis());
                if (null != apiResponse) {
                    try {
                        apiResponse.close();
                    } catch (Throwable e) {
                    }
                }
                notifyMessage.put(NotifyMessage.PUSH_RESP, response);
                notifyMessage.put(NotifyMessage.IS_SUCCESS, isSuccess);
            }
        });
    }



    @Trace
    @Timed("arg[0]")
    public void traceCall(String url, Runnable runnable) {
        runnable.run();
    }

    private Map<String, Object> getPayload(Map<String, Object> notifyMessage, String appId) {
        Map<String, Object> payload = new HashMap<>();
        for (Map.Entry<String, Object> entry : notifyMessage.entrySet()) {
            String key = entry.getKey();
            if (excludeKeys.contains(key))
                continue;
            payload.put(key, entry.getValue());
        }

        //根据apollo配置，key：SETTLEMENT_AMOUNT_DISPLAY_FILTER， 判断是否需要发送结算金额,默认是发送
        if (ApolloUtil.getSettlementFilter().contains(appId)){
            payload.remove(NotifyMessage.SETTLEMENT_AMOUNT);
        }

        return payload;
    }

    /**
     * 对请求消息提进行签名
     *
     * @param appId     公钥
     * @param appSecret 私钥
     * @param body      消息体
     * @return 消息头Authorization
     */
    private Map<String, String> getAuthorizationHeader(String appId, String appSecret, String body) {
        byte[] bytes;
        try {
            bytes = body.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.info("cannot get bytes from payload", e);
            bytes = new byte[0];
        }
        String sign = Md5Util.hash(bytes, appSecret.getBytes());
        String authorization = appId + " " + sign;
        return MapUtil.hashMap("Authorization", authorization);
    }

    private String getAuthorization(String appId, String appSecret, String body) {
        byte[] bytes;
        try {
            bytes = body.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.info("cannot get bytes from payload", e);
            bytes = new byte[0];
        }
        String sign = Md5Util.hash(bytes, appSecret.getBytes());
        String authorization = appId + " " + sign;
        return authorization;
    }

    @Override
    public void createNotifyConfig(Map<String, Object> notifyConfig) throws EnterpriseException {
        if (StringUtils.isEmpty(BeanUtil.getPropString(notifyConfig, NotifyConfig.OBJECT_ID))
                || StringUtils.isEmpty(BeanUtil.getPropString(notifyConfig, NotifyConfig.APP_ID))
                || StringUtils.isEmpty(BeanUtil.getPropString(notifyConfig, NotifyConfig.APP_SECRET))
                || StringUtils.isEmpty(BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_RETRY))
                || StringUtils.isEmpty(BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_TYPE))) {
            throw new EnterpriseException(Constants.CODE_REQ_FAILURE,
                    "object_id、app_id、app_secret、notify_retry和notify_type都不能为空");
        }
        int objectType = BeanUtil.getPropInt(notifyConfig, NotifyConfig.OBJECT_TYPE, NotifyConfig.OBJECT_TYPE_MERCHANT);
        if (!(objectType == NotifyConfig.OBJECT_TYPE_MERCHANT || objectType == NotifyConfig.OBJECT_TYPE_VENDOR
                || objectType == NotifyConfig.OBJECT_TYPE_AGENT || objectType == NotifyConfig.OBJECT_TYPE_GROUP
                || objectType == NotifyConfig.OBJECT_TYPE_MULTISTAGE_AGENT || objectType == NotifyConfig.OBJECT_TYPE_STORE || objectType == NotifyConfig.OBJECT_TYPE_ALIPAY_SERVICE)) {
            throw new EnterpriseException(Constants.CODE_REQ_FAILURE,
                    "object_type 错误，不为 1：商户推送；2：服务商推送；3：代理商商户推送；4：集团商户推送；5：多级代理商商户推送;6：指定店铺推送");
        }
        int notifyType = BeanUtil.getPropInt(notifyConfig, NotifyConfig.NOTIFY_TYPE, NotifyConfig.NOTIFY_TYPE_DEFAULT);
        if (!(notifyType == NotifyConfig.NOTIFY_TYPE_DEFAULT || notifyType == NotifyConfig.NOTIFY_TYPE_CUSTOMIZED
                || notifyType == NotifyConfig.NOTIFY_TYPE_HUAWEI)) {
            throw new EnterpriseException(Constants.CODE_REQ_FAILURE, "notify_type 错误，不为 0：默认推送类型；1：特殊推送类型；2：华为推送");
        }
        if(!notifyConfig.containsKey(NotifyConfig.NOTIFY_PAYWAY_TYPE)) {
            notifyConfig.put(NotifyConfig.NOTIFY_PAYWAY_TYPE, NotifyConfig.NOTIFY_PAYWAY_TYPE_NON_CHARGE);
        }
        tradeNotifyBiz.addNotifyConfig(notifyConfig);
    }

    @Override
    public void createNotifyConfigForAliPay(String merchantSn) {
        try {
            Map<String, Object> notifyConfig = new HashMap<>();
            String merchantId = businssCommonService.getMerchantIdBySn(merchantSn);
            Map<String, Object> notifyAliPayConfig = tradeNotifyBiz.getNotifyConfig(NotifyConfig.OBJECT_TYPE_ALIPAY_SERVICE, merchantId);
            if (notifyAliPayConfig != null) {
                return;
            }
            // 不存在配置则新增
            notifyConfig.put(NotifyConfig.OBJECT_ID, merchantId);
            notifyConfig.put(NotifyConfig.OBJECT_TYPE, NotifyConfig.OBJECT_TYPE_ALIPAY_SERVICE);
            notifyConfig.put(NotifyConfig.APP_ID, "alipay");
            notifyConfig.put(NotifyConfig.APP_SECRET, "alipay");
            notifyConfig.put(NotifyConfig.NOTIFY_URL, "https://openapi.alipay.com/gateway.do");
            notifyConfig.put(NotifyConfig.NOTIFY_GATEWAY, "");
            notifyConfig.put(NotifyConfig.NOTIFY_STATUS, "");
            notifyConfig.put(NotifyConfig.NOTIFY_RESP, "success");
            notifyConfig.put(NotifyConfig.NOTIFY_RETRY, "60,300,600");
            notifyConfig.put(NotifyConfig.MERCHANT_SN, merchantSn);
            notifyConfig.put(NotifyConfig.NOTIFY_TYPE, 0);
            notifyConfig.put(NotifyConfig.NOTIFY_PAYWAY_TYPE, 0);
            Map extra = new HashMap();
            notifyConfig.put(NotifyConfig.EXTRA, extra);
            createNotifyConfig(notifyConfig);
        } catch (Exception e) {
            logger.error("create notify config for alipay error: " + e.getMessage(), e);
        }
    }



    @Override
    public Map<String, Set<String>> getGroupMerchantCache() {
        return LOCAL_GROUP_MERCHANT_CACHE;
    }

    @Override
    public void updateGroupMerchantCache(Map<String, Set<String>> request) {
        LOCAL_GROUP_MERCHANT_CACHE.clear();
        if (null != request) {
            LOCAL_GROUP_MERCHANT_CACHE.putAll(request);
        }

    }

    @Override
    public void deleteNotifyTradeConfigByObjectTypeAndObjectId(int objectType, String objectId) {
        tradeNotifyBiz.deleteNotifyConfigByObjectTypeAndObjectId(objectType, objectId);
    }

    @Override
    public void updateNotifyConfig(Map notifyConfig) {
        tradeNotifyBiz.updatePart(notifyConfig);
    }

    @Override
    public Map findNotifyConfigByObjecTypeAndObjectId(int objectType,String objectId) {
       return   tradeNotifyBiz.getNotifyConfig(objectType, objectId);
    }

    @Override
    public void updateNotifyUrlByAppId(String appId,String notifyUrl) {
        tradeNotifyBiz.updateNotifyUrlByAppId(appId,notifyUrl);
    }

    @Override
    public String queryOneAppSecretByAppId(String appId) {
        return tradeNotifyBiz.queryOneAppSecretByAppId(appId);
    }


    public void reNotify(String tsn, String merchantId) {
        Map<String, Object> params = CollectionUtil.hashMap(
                "transaction_sn", tsn
        );
        if(!StringUtils.isEmpty(merchantId)){
            params.put(Store.MERCHANT_ID, merchantId);
        }
        List<Map> list = transactionServiceV2.getTransactionList(new PageInfo(1, 1), params).getRecords();
        if (list == null || list.size() != 1) {
            throw new EnterpriseException(Constants.CODE_NOT_FOUND, "流水不存在" + tsn);
        }
        Map transactionEx = list.get(0);
        List<Map<String,Object>> notifyConfigList = getNotifyConfig(transactionEx);
        notifyConfigList.forEach(notifyConfig -> {
            try {
                TraceUtil.createTraceId();
                notifyTrade(transactionEx, RE_SEND, notifyConfig);
            } catch (Exception e) {
                String notifyUrl = MapUtil.getString(notifyConfig, NotifyMessage.NOTIFY_URL);
                logger.error("第{}次推送数据失败, notifyUrl={}, 对应的流水信息={}", RE_SEND, notifyUrl, JSONObject.toJSONString(transactionEx), e);
            } finally {
                TraceUtil.removeTraceId();
            }
        });
    }

    private Map<String, Object> getTerminalById(String terminalId) {
        Map terminal = terminalService.getTerminal(terminalId);
        if (null != terminal) {
            return CollectionUtil.hashMap(Terminal.DEVICE_FINGERPRINT, terminal.get(Terminal.DEVICE_FINGERPRINT), Terminal.VENDOR_APP_APPID, terminal.get(Terminal.VENDOR_APP_APPID));
        }
        return null;
    }

    private String getDeviceSnByDeviceFingerprint(String deviceFingerprint){
        Map<String,Object> device = deviceQrcodeService.getIotByQrcode(CollectionUtil.hashMap(
                IotDeviceQrcode.QRCODE, deviceFingerprint
        ));
        return MapUtil.getString(device, IotDeviceQrcode.DEVICE_SN);
    }

    /**
     * 获取硬件sn, 默认为 deviceFingerprint
     * @param deviceFingerprint
     * @param vendorAppAppId
     * @return
     */
    private String getDeviceSn(String deviceFingerprint, String vendorAppAppId){
        if(deviceFingerprint == null || vendorAppAppId == null){
            return deviceFingerprint;
        }
        String deviceSn = null;
        if(ApolloUtil.getVendorAppIotAppIds().contains(vendorAppAppId)){
            try{
                deviceSn =  localDeviceSnCache.get(deviceFingerprint);
            }catch (Exception e){
                logger.info("get device info error: " + deviceFingerprint, e);
            }
        }
        return StringUtils.isEmpty(deviceSn) ? deviceFingerprint : deviceSn;
    }

    static class BooleanWrapper {
        Boolean flag;
        BooleanWrapper(Boolean flag) {
            this.flag = flag;
        }
    }
}
