package com.wosai.bsm.enterprise.util;

import com.alibaba.fastjson.JSONArray;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.wosai.mpay.util.JsonUtil;
import lombok.SneakyThrows;

import java.util.*;


public class ApolloUtil {
	private static Config config = null;

	private static final String SETTLEMENT_AMOUNT_DISPLAY_FILTER = "settlement_amount_display_filter";
	private static final String GATEWAY_LIMIT_TIME = "gateway_limit_time";
	private static final String APP_ID_LIMIT = "app_id_limit";
	private static final String VENDOR_APP_IOT_APPIDS = "vendor_app_iot_appids"; //收钱音箱等iot设备的应用id
	private static final String HUAWEI_PUSH_URLS= "huawei_push_urls";
	//富士康推送url
	private static final String FOXCONN_PUSH_URLS= "foxconn_push_urls";

	private static final String ZJTL_PUSH_URLS= "zjtl_push_urls";

	public static final String ZJTL_DECRYPT_PARAMS = "zjtl_decrypt_params"; // 浙江泰隆解密参数

	public static String HOPE_EDU_PARAMS = "hope_edu_sign_params"; // 院校通签名验签参数

	private static final String NON_REALTIME_PUSH_DISABLE_CONFIG= "non_realtime_push_disable_config"; //不做补推的时间配置  ["11:30:00-12:30:00", "17:15:00-18:45:00"]

	private static String PAYWAY_LIMIT = "payway_limit";


		private static String ZJTL_NOTIFY_SERVICE_ID = "zjtl_notify_service_id";

	private  static  List<String> zjtlUrls = new ArrayList();


	private static String ICD_PUSH_URLS = "icd_push_urls";
	private static List<String> icdUrls = new ArrayList<>();

	private static String CRLAND_PUSH_URLS = "crland_push_urls";
	private static List<String> crLandUrls = new ArrayList<>();


	private static String TECH_TRANS_PUSH_URLS = "tech_trans_push_urls";


	private static List<String> techTransUrls = new ArrayList<>();




	private static String HDCRE_CONFIG = "hdcre_config";

	private static Map<String, Map<String,String>> hdcreConfig = new HashMap<>();

	private static String TECH_TRANS_V2_PUSH_URLS = "tech_trans_v2_push_urls";


	private static List<String> techTransV2Urls = new ArrayList<>();

	private static String TECH_TRANS_V3_PUSH_URLS = "tech_trans_v3_push_urls";


	private static List<String> aliPayMerchantTradeUrls = new ArrayList<>();
	private static final String ALIPAY_MERCHANT_TRADE_URLS = "alipay_merchant_trade_urls";


	private static String HOPE_EDU_PUSH_URLS = "hope_edu_push_urls";

	private static List<String> hopeEduPushUrls = new ArrayList<>();







	private static Map<String, Map<String, String>> techTransV3Urls = new HashMap<>();



	private static Map<String, String> zjtlDecryptParamsMap = new HashMap<>();

	private static String zjtlNotifyServiceId = null;



	private static Boolean paywayLimit = null;

	static{
		config = ConfigService.getAppConfig();
		initPaywayLimit();
		loadZJTLDecryptParams();
		buildZJTLUrls();
		buildICDUrls();
		buildCrLandUrls();
		buildTechTransUrls();
		buildHdcreConfig();
		buildTechTransV2Urls();
		buildTechTransV3Urls();
		buildAliPayMerchantTradeUrls();
		buildHopeEduPushUrls();

		config.addChangeListener(new ConfigChangeListener() {
			@Override
			public void onChange(ConfigChangeEvent listener) {
				if (listener.isChanged(PAYWAY_LIMIT)) {
					initPaywayLimit();
				}
				if(listener.isChanged(ZJTL_DECRYPT_PARAMS)) {
					loadZJTLDecryptParams();
				}
				if(listener.isChanged(ZJTL_PUSH_URLS)) {
					buildZJTLUrls();

				}

				if (listener.isChanged(ICD_PUSH_URLS)) {
					buildICDUrls();
				}

				if (listener.isChanged(CRLAND_PUSH_URLS)) {
					buildICDUrls();
				}

				if (listener.isChanged(TECH_TRANS_PUSH_URLS)) {
					buildTechTransUrls();
				}

				if (listener.isChanged(HDCRE_CONFIG)) {
					buildHdcreConfig();
				}

				if (listener.isChanged(TECH_TRANS_V2_PUSH_URLS)) {
					buildTechTransV2Urls();
				}
				if (listener.isChanged(TECH_TRANS_V3_PUSH_URLS)) {
					buildTechTransV3Urls();
				}
				if (listener.isChanged(ALIPAY_MERCHANT_TRADE_URLS)) {
					buildAliPayMerchantTradeUrls();
				}
				if (listener.isChanged(HOPE_EDU_PUSH_URLS)) {
					buildHopeEduPushUrls();
				}

			}
		});
	}

	public static String getSettlementFilter(){
		return config.getProperty(SETTLEMENT_AMOUNT_DISPLAY_FILTER, "");
	}

	public static int getGatewayLimitTime(){
		return config.getIntProperty(GATEWAY_LIMIT_TIME, 2000);
	}

	public static boolean isAppIdLimit(String appId){
		return config.getProperty(APP_ID_LIMIT, "").contains(appId);
	}

	public static String getVendorAppIotAppIds(){
		return config.getProperty(VENDOR_APP_IOT_APPIDS, "");
	}

	public static List<String> getHuiWeiPushUrls(){
		String urlString = config.getProperty(HUAWEI_PUSH_URLS,"");
		return JSONArray.parseArray(urlString, String.class);
	}

	/**
	 * 获取富士康推送地址
	 *
	 * @return
	 */
	public static List<String> getFoxconnPushUrls() {
		String urlString = config.getProperty(FOXCONN_PUSH_URLS,"");
		return JSONArray.parseArray(urlString, String.class);
	}

	private static void initPaywayLimit(){
		paywayLimit = config.getBooleanProperty(PAYWAY_LIMIT, false);
	}

	public static boolean  getPaywayLimit() {
		return paywayLimit;
	}

	@SneakyThrows
	public static List<String> getNonRealtimePushDisableConfig(){
		return JsonUtil.jsonStringToObject(config.getProperty(NON_REALTIME_PUSH_DISABLE_CONFIG, "[]"), List.class);
	}


	public static List<String> getZJTLUrls(){
		return zjtlUrls;

	}

	public static List<String> getICDUrls() {
		return icdUrls;

	}


	public static List<String> getCrLandUrls() {
		return crLandUrls;
	}


	public static List<String> getTechTransUrls() {
		return techTransUrls;
	}


	public static Map<String, Map<String,String>> getHdcreConfig() {
		return hdcreConfig;
	}


	public static List<String> getTechTransV2Urls() {
		return techTransV2Urls;
	}

	public static List<String> getHopeEduPushUrls() {
		return hopeEduPushUrls;
	}

	public static Map<String, Map<String, String>> getTechTransV3Urls() {
		return techTransV3Urls;
	}

	public static List<String> getAliPayMerchantTradeUrls() {
		return aliPayMerchantTradeUrls;
	}










	private static void loadZJTLDecryptParams() {
		String zjtlDecryptParamsStr = config.getProperty(ZJTL_DECRYPT_PARAMS, "{}");
		zjtlDecryptParamsMap = JsonUtil.jsonStrToObject(zjtlDecryptParamsStr, Map.class);

	}

	public static Map<String, String> getZJTLDecryptParams() {

		return zjtlDecryptParamsMap;
	}

	private static  void buildZJTLUrls() {
		String urlString = config.getProperty(ZJTL_PUSH_URLS,"");
		zjtlUrls =  JSONArray.parseArray(urlString, String.class);
	}


	public static void buildICDUrls() {
		String urlString = config.getProperty(ICD_PUSH_URLS, "[]");
		icdUrls = JSONArray.parseArray(urlString, String.class);
	}


	public static void buildCrLandUrls() {
		String urlString = config.getProperty(CRLAND_PUSH_URLS, "[]");
		crLandUrls = JSONArray.parseArray(urlString, String.class);
	}


	public static void buildTechTransUrls() {
		String urlString = config.getProperty(TECH_TRANS_PUSH_URLS, "[]");
		techTransUrls = JSONArray.parseArray(urlString, String.class);
	}


	public static void buildHdcreConfig() {
		String urlString = config.getProperty(HDCRE_CONFIG, "{}");
		hdcreConfig = JsonUtil.jsonStrToObject(urlString, Map.class);
	}


	public static void buildTechTransV2Urls() {
		String urlString = config.getProperty(TECH_TRANS_V2_PUSH_URLS, "[]");
		techTransV2Urls = JSONArray.parseArray(urlString, String.class);
	}


	public static void buildTechTransV3Urls() {
		String urlString = config.getProperty(TECH_TRANS_V3_PUSH_URLS, "{}");
		techTransV3Urls = JsonUtil.jsonStrToObject(urlString, Map.class);
	}


	public static void buildAliPayMerchantTradeUrls() {
		String urlString = config.getProperty(ALIPAY_MERCHANT_TRADE_URLS, "[]");
		aliPayMerchantTradeUrls = JSONArray.parseArray(urlString, String.class);
	}

	public static void buildHopeEduPushUrls() {
		String urlString = config.getProperty(HOPE_EDU_PUSH_URLS, "[]");
		hopeEduPushUrls = JSONArray.parseArray(urlString, String.class);
	}








	private static void loadZJTLNotifyServiceId() {
		String str = config.getProperty(ZJTL_NOTIFY_SERVICE_ID, "{}");
		zjtlNotifyServiceId = str;
	}

	public static String getZJTLNotifyServiceId() {
		return zjtlNotifyServiceId;
	}

}
