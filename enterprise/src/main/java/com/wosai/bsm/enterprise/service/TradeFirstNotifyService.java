package com.wosai.bsm.enterprise.service;

import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.biz.TradeNotifyBiz;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.bsm.enterprise.util.GatewayAnalysis;
import com.wosai.bsm.enterprise.util.TraceUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
public class TradeFirstNotifyService {
    private static Logger logger = LoggerFactory.getLogger(TradeFirstNotifyService.class);
    private static AtomicBoolean isStop = new AtomicBoolean(false);
    private final ThreadPoolExecutor INIT_EXECUTOR = new ThreadPoolExecutor(200, 200, 0L, TimeUnit.MILLISECONDS,
            new SynchronousQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static long[] delays = {1, 5, 30, 600, 60, 300, 1800, 3600, 3600, 3600, 3600, 86400};

    @Autowired
    TradeNotifyBiz tradeNotifyBiz;
    @Autowired
    ApplicationContext context;
    TradeNotifyService tradeNotifyService;

    @Resource(name = "threadPool")
    private ScheduledExecutorService executor;

    @PostConstruct
    public void post() {
        tradeNotifyService = context.getBean(TradeNotifyService.class);
        Runtime.getRuntime().addShutdownHook(new Thread(() ->{
            isStop.set(true);
            long start = System.currentTimeMillis();
            while(INIT_EXECUTOR.getQueue().size() > 0) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
                if(System.currentTimeMillis() - start > 5 * 1000) {
                    break;
                }
            }
            int cnt = INIT_EXECUTOR.getQueue().size();
            if(cnt > 0) {
                logger.warn("线程池中还有{}条数据未处理", cnt);
            }
            INIT_EXECUTOR.shutdown();
        }));
    }

    public void push(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage){
        String traceId = TraceUtil.getTraceId();
        if(traceId == null){
            traceId = TraceUtil.genTraceId();
        }
        String finalTraceId = traceId;
        CompletableFuture.runAsync(() -> {
            try{
                TraceUtil.setTraceId(finalTraceId);
                tracePush(notifyConfig, notifyMessage);
            }finally {
                TraceUtil.removeTraceId();
            }
        }, INIT_EXECUTOR);
    }

    @Trace(operationName = "notify")
    protected void tracePush(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage) {
            Runnable retry = new Runnable() {
                @Override
                public void run() {
                    Integer status = MapUtil.getInteger(notifyMessage, NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR);
                    if (status.equals(NotifyMessage.PUSH_SUCCESS)) {
                        return;
                    }

                    int pushCount = MapUtil.getIntValue(notifyMessage, NotifyMessage.PUSH_COUNT);
                    //重推4次
                    if (pushCount > 4) {
                        return;
                    }

                    String appId = MapUtil.getString(notifyConfig, NotifyConfig.APP_ID);
                    String notifyUrl = MapUtil.getString(notifyConfig, NotifyConfig.NOTIFY_URL);
                    if (ApolloUtil.isAppIdLimit(appId) || GatewayAnalysis.isLimit(notifyUrl)) {
                        // appid推送受限
                        logger.warn("gateway push limit, app_id：{}, notify_url：{}, avg：{}, tsn：{}",
                                appId, notifyUrl, GatewayAnalysis.getAvg(notifyUrl), MapUtil.getString(notifyConfig, DaoConstants.ID));
                        if(pushCount == 0) {
                            notifyMessage.put(NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR);
                        }

                    } else if (isStop.get()) {
                        // 结束程序，将所有未推送数据状态置为失败，在定时任务中重新发送，启动后重新发送
                        tradeNotifyBiz.getNotifyMessageDao()
                                .updatePart(CollectionUtil.hashMap(DaoConstants.ID, notifyMessage.get(DaoConstants.ID),
                                        NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR
                                ));
                        return;
                    } else {
                        try {
                            tradeNotifyService.notifyMessage(notifyMessage, notifyConfig);
                        } catch (EnterpriseException e) {
                            logger.error("发送消息异常: notifyUrl={}, error={}", notifyUrl, e.getMessage(), e);
                        }
                    }
                    pushCount = MapUtil.getInteger(notifyMessage, NotifyMessage.PUSH_COUNT);
                    long delayTime = delays[pushCount];

                    try {
                        tradeNotifyBiz.saveNotifyMessage(notifyConfig, notifyMessage);
                    } catch (Exception e) {
                        logger.error("保存消息异常: notifyUrl={}, error={}", notifyUrl, e.getMessage(), e);
                    }
                    executor.schedule(this, delayTime, TimeUnit.SECONDS);

                }
            };
            retry.run();

    }


}
