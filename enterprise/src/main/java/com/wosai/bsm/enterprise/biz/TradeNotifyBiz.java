package com.wosai.bsm.enterprise.biz;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.bean.NotifyMessage;
import com.wosai.bsm.enterprise.ex.EnterpriseException;
import com.wosai.bsm.enterprise.service.TradeFirstNotifyService;
import com.wosai.bsm.enterprise.service.TradeNotifyServiceImpl;
import com.wosai.bsm.enterprise.util.ApolloUtil;
import com.wosai.bsm.enterprise.util.DateUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.util.*;

import static com.wosai.bsm.enterprise.util.Constants.SERIALIZATION_ERROR;


@Service
public class TradeNotifyBiz {
    private static final Logger logger = LoggerFactory.getLogger(TradeNotifyBiz.class);
    public static final String UPDATE_PART_SQL = "sql";
    public static final String UPDATE_PART_BINDING = "binding";
    @Autowired
    private JdbcTemplate enterpriseJdbcTemplate;
    @Autowired
    private Dao<Map<String, Object>> notifyMessageDao;

    @Autowired
    ObjectMapper objectMapper;


    @Autowired
    private LarkClient larkClient;

    public void updateNotifyUrlByAppId(String appId, String notifyUrl) {
        String sql = "update notify_config set  notify_url = ? ,mtime =  UNIX_TIMESTAMP()*1000 where app_id  = ?";
        enterpriseJdbcTemplate.update(sql, notifyUrl, appId);
    }

    public void deleteNotifyConfigByObjectTypeAndObjectId(int objectType, String objectId) {
        String sql = "select * from notify_config where object_type = ? and object_id = ? ";
        Map<String, Object> notifyConfig = enterpriseJdbcTemplate.queryForMap(sql, objectType, objectId);
        sql = "delete from notify_config where object_type = ?  and object_id = ?";
        Object args[] = new Object[]{objectType,objectId};
        enterpriseJdbcTemplate.update(sql, args);
        //记录删除配置，方便删除后恢复。
        logger.info("delete notifyTradeConfig {}", notifyConfig);
    }

    @Cacheable("NotifyConfig")
    public List<Map<String, Object>> queryNotifyConfigByObjectType(int objectType) {
        String sql = "select * from notify_config where object_type = ?";
        try {
            List<Map<String, Object>> notifyConfigList = enterpriseJdbcTemplate.queryForList(sql, objectType);
            for (Map<String, Object> notifyConfig : notifyConfigList) {
                convertNotifyConfigExtraType(notifyConfig);
            }
            return notifyConfigList;
        } catch (EmptyResultDataAccessException ignored) {
        }
        return null;
    }

    @Cacheable("NotifyConfig")
    public Map<String, Object> queryNotifyConfig(int objectType, String objectId) {
        String sql = "select * from notify_config where object_type = ? and object_id = ?";
        try {
            Map<String, Object> notifyConfig = enterpriseJdbcTemplate.queryForMap(sql, objectType, objectId);
            convertNotifyConfigExtraType(notifyConfig);
            return notifyConfig;
        } catch (EmptyResultDataAccessException ignored) {
        }
        return null;
    }

    public void deleteNotifyMessageById(long messageId){
        String sql = "DELETE FROM notify_message WHERE id = ?";
        enterpriseJdbcTemplate.update(sql, messageId);
    }


    public Map<String, Object> getNotifyConfig(int objectType, String objectId) {
        String sql = "select * from notify_config where object_type = ? and object_id = ?";
        try {
            Map<String, Object> notifyConfig = enterpriseJdbcTemplate.queryForMap(sql, objectType, objectId);
            convertNotifyConfigExtraType(notifyConfig);
            return notifyConfig;
        } catch (EmptyResultDataAccessException ignored) {
        }
        return null;
    }

    @Cacheable("NotifyConfig")
    public Map<String, Object> queryNotifyConfigByIdAndUrl(String configId, String notifyUrl) {
        String sql = "select * from notify_config where object_id = ?";
        Map<String, Object> notifyConfig = null;
        List<Map<String, Object>> notifyConfigList = enterpriseJdbcTemplate.queryForList(sql, configId);
        for (Map<String, Object> config : notifyConfigList) {
            String configUrl = MapUtils.getString(config, NotifyConfig.NOTIFY_URL, "");
            if (configUrl.contains(notifyUrl)) {
                notifyConfig = config;
                break;
            }
        }
        convertNotifyConfigExtraType(notifyConfig);
        return notifyConfig;
    }

    /**
     * 将配置中的extra字段 由字节数组转化为String
     *
     * @param notifyConfig
     */
    public void convertNotifyConfigExtraType(Map<String, Object> notifyConfig) {
        if (notifyConfig != null) {
            // 得到配置信息
            Object extraMsg = notifyConfig.get(NotifyConfig.EXTRA);
            // 额外配置信息字节转化为字符串
            if (Objects.nonNull(extraMsg)) {
                byte[] arr = (byte[]) (extraMsg);
                notifyConfig.put(NotifyConfig.EXTRA, new String(arr));
            }
        }
    }

    public String queryOneAppSecretByAppId(String appId) {
        String sql = "select * from notify_config where app_id = ? limit 1 ";
        try {
            Map<String, Object> notifyConfig = enterpriseJdbcTemplate.queryForMap(sql, appId);
            String appSercret = MapUtils.getString(notifyConfig, NotifyConfig.APP_SECRET);
            appSercret = Base64.getEncoder().encodeToString(appSercret.getBytes(StandardCharsets.UTF_8));
            return appSercret;
        } catch (EmptyResultDataAccessException ignored) {
        }
        return null;
    }

    public List<Integer> getRetryIntervals(String notifyRetry) {
        List<Integer> retryIntervals = new ArrayList<>();
        if (StringUtils.isBlank(notifyRetry))
            return retryIntervals;
        for (String interval : notifyRetry.split(",")) {
            try {
                retryIntervals.add(Integer.parseInt(interval.trim()));
            } catch (NumberFormatException e) {
            }
        }
        return retryIntervals;
    }

    public void addInitNotifyMessage(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage) {
        addInitNotifyMessage(notifyConfig, notifyMessage, TradeNotifyServiceImpl.FIRST_SEND);
    }

    public void addInitNotifyMessage(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage, String notifyType) {
        String objectId = MapUtils.getString(notifyConfig, NotifyConfig.OBJECT_ID);
        String date = DateUtil.getToday("yyyy-MM-dd");
        notifyMessage.put(NotifyMessage.PUSH_DATE, date);
        notifyMessage.put(NotifyMessage.OBJECT_ID, objectId);
        if(notifyType.equals(TradeNotifyServiceImpl.FIRST_SEND )){
            notifyMessage.put(NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_INIT);
        } else if(notifyType.equals(TradeNotifyServiceImpl.RE_SEND)) {
            //置状态为失败，重推
            notifyMessage.put(NotifyMessage.PUSH_STATUS, NotifyMessage.PUSH_ERROR);
        }
        notifyMessage.put(NotifyMessage.PUSH_TIME, System.currentTimeMillis());
        notifyMessage.put(NotifyMessage.PUSH_COUNT, 0);
        notifyMessage.put(NotifyMessage.REFLECT, removeEmoji(notifyMessage.get(NotifyMessage.REFLECT)));

        addExtra(notifyMessage);

        try {
            notifyMessageDao.save(notifyMessage);
        } catch (Exception e) {
            logger.error("交易推送数据保存失败", e);
            larkClient.sendMsg("交易推送数据保存失败:sn-client_tsn", MapUtils.getString(notifyMessage, NotifyMessage.SN) + "-" + MapUtils.getString(notifyMessage, NotifyMessage.CLIENT_TSN));

        }


        buildParams(notifyMessage);

    }

    public void saveNotifyMessage(Map<String, Object> notifyConfig, Map<String, Object> notifyMessage) {
        Boolean isSuccess = MapUtil.getBoolean(notifyMessage, NotifyMessage.IS_SUCCESS, false);
        //只是判断的一个标识，需要移除,不然save操作就会报错
        notifyMessage.remove(NotifyMessage.IS_SUCCESS);

        String notifyUrl = MapUtil.getString(notifyMessage, NotifyMessage.NOTIFY_URL);
        String objectId = MapUtils.getString(notifyConfig, NotifyConfig.OBJECT_ID);
        String date = DateUtil.getToday("yyyy-MM-dd");
        notifyMessage.put(NotifyMessage.PUSH_DATE, date);
        notifyMessage.put(NotifyMessage.OBJECT_ID, objectId);

        int pushCount = MapUtils.getIntValue(notifyMessage, NotifyMessage.PUSH_COUNT);
        int status = NotifyMessage.PUSH_SUCCESS;
        if (!isSuccess) {
            if (pushCount < 12) {
                status = NotifyMessage.PUSH_ERROR;
            } else {
                status = NotifyMessage.PUSH_FAILURE;
                logger.warn("push exceeded maximum number of retries, sn = {}, client_tsn = {} ",
                        MapUtils.getString(notifyMessage, NotifyMessage.SN),
                        MapUtils.getString(notifyMessage, NotifyMessage.CLIENT_TSN));
                if (ApolloUtil.getHuiWeiPushUrls().contains(notifyUrl)) {
                    larkClient.sendMsg("华为推送失败订单号sn-client_tsn", MapUtils.getString(notifyMessage, NotifyMessage.SN) + "-" + MapUtils.getString(notifyMessage, NotifyMessage.CLIENT_TSN));
                }
            }
        }
        notifyMessage.put(NotifyMessage.PUSH_STATUS, status);
        //只推12次
        Long pushTime = 0L;
        if (pushCount < 12) {
            pushTime = TradeFirstNotifyService.delays[pushCount] * 1000  + System.currentTimeMillis() ;
        } else {
            pushTime = System.currentTimeMillis();
        }

        notifyMessage.put(NotifyMessage.PUSH_TIME, pushTime);
        notifyMessage.put(NotifyMessage.PUSH_COUNT, ++pushCount);
        notifyMessage.put(NotifyMessage.REFLECT, removeEmoji(notifyMessage.get(NotifyMessage.REFLECT)));
        notifyMessage.put(NotifyMessage.NOTIFY_URL, notifyUrl);

        addExtra(notifyMessage);
        notifyMessageDao.save(notifyMessage);
        buildParams(notifyMessage);
        //根据version来 update，这里需要手动加一下
        int version = MapUtil.getInteger(notifyMessage, NotifyMessage.VERSION);
        notifyMessage.put(NotifyMessage.VERSION, ++version);
    }

    public List<Map<String, Object>> getErrorNotifyMessages() {
        List<Criteria> list =new ArrayList<>();
        Criteria criteria  = Criteria.where(NotifyMessage.PUSH_STATUS).is(NotifyMessage.PUSH_ERROR);
        //超过实时推送次数，且推送时间小于当前时间
        list.add(Criteria.and(Criteria.where(NotifyMessage.PUSH_COUNT).gt(4),Criteria.where(NotifyMessage.PUSH_TIME).lt(System.currentTimeMillis())));
        //20分钟之前且小于4次，已经没在实时推送里，在这重推
        list.add(Criteria.and(Criteria.where(NotifyMessage.PUSH_COUNT).lt(5),Criteria.where(NotifyMessage.PUSH_TIME).lt(System.currentTimeMillis() - 1200 * 1000)));
        criteria.withOr(list);

        Filter filter = notifyMessageDao.filter(criteria);
        filter.limit(1000);
        List<Map<String, Object>> results = IteratorUtils.toList(filter.fetchAll());
        for (Map result : results) {
            byte[] arr = (byte[]) result.get(NotifyMessage.EXTRA);
            if (Objects.isNull(arr) || arr.length == 0) {
                continue;
            }

            Map<String, Object> extraMap = JSONObject.parseObject(arr, Map.class);
            result.remove(NotifyMessage.EXTRA);

            if (MapUtil.isEmpty(extraMap)) {
                continue;
            }
            //将扩展字段里的属性, 放到result中
            for (String key : NotifyMessage.EXTRA_KEY) {
                Object value = extraMap.get(key);
                if (value == null) {
                    continue;
                }
                result.put(key, value);
            }
        }
        return results;
    }

    /**
     * 获取华为数据的统计信息
     * @param merchantSnList
     * @param status
     * @return
     */
    public Map<String, Object> getHuaweiPushNotifyMessagesSummaryInfo(Set<String> merchantSnList, List<Integer> status, long startTime, long endTime) {
        Map<String, Object> map = new HashMap<>();
        for (String merchantSn : merchantSnList) {
            Criteria criteria = Criteria.where(NotifyMessage.MERCHANT_SN).is(merchantSn);
            if (!CollectionUtils.isEmpty(status)) {
                criteria.with(NotifyMessage.PUSH_STATUS).in(status);
            }
            if (!CollectionUtils.isEmpty(ApolloUtil.getHuiWeiPushUrls())) {
                criteria.with(NotifyMessage.NOTIFY_URL).in(ApolloUtil.getHuiWeiPushUrls());
            }
            criteria.with(NotifyMessage.CTIME).lt(endTime);
            criteria.with(NotifyMessage.CTIME).gt(startTime);
            Filter filter = notifyMessageDao.filter(criteria);
            long count = filter.count();
            map.put(merchantSn, count);
        }
        return map;
    }


    //排除和生成extra
    public void addExtra(Map<String, Object> notifyMessage) {
        Map<String, Object> extra = new HashMap<>();
        for (String key : NotifyMessage.EXTRA_KEY) {
            Object value = notifyMessage.get(key);
            if(value != null) {
                extra.put(key, value);
            }
        }
        notifyMessage.put(NotifyMessage.EXTRA, JSONObject.toJSONString(extra));
        for (String key : NotifyMessage.EXTRA_KEY) {
            notifyMessage.remove(key);
        }
    }

    public static void buildParams(Map<String, Object> notifyMessage) {
        String extraString = MapUtil.getString(notifyMessage, NotifyMessage.EXTRA);
        if(Objects.isNull(extraString)) {
            return;
        }

        Map<String, Object> extraMap = JSONObject.parseObject(extraString, Map.class);
        notifyMessage.remove(NotifyMessage.EXTRA);

        if(MapUtil.isEmpty(extraMap)) {
            return;
        }

        //将扩展字段里的属性, 放到notifyMessage中
        for (String key : NotifyMessage.EXTRA_KEY) {
            Object value = extraMap.get(key);
            if (value == null) {
                continue;
            }
            notifyMessage.put(key, value);
        }
    }

    public List<Map<String, Object>> getNotPushNotifyMessages() {
        Criteria criteria = Criteria.where(NotifyMessage.PUSH_STATUS).is(NotifyMessage.PUSH_INIT)
                .with(NotifyMessage.PUSH_TIME).gt(System.currentTimeMillis() - 6 * 3600 * 1000)
                .with(DaoConstants.CTIME).lt(System.currentTimeMillis() - 600 * 1000);
        Filter filter = notifyMessageDao.filter(criteria, CollectionUtil.hashSet(DaoConstants.ID));
        filter.limit(500);
        return IteratorUtils.toList(filter.fetchAll());
    }

    public void addNotifyConfig(Map<String, Object> notifyConfig) {
        // SQL
        String sql = "INSERT INTO `notify_config` (`object_type`, `object_id`, `app_id`, `app_secret`, `notify_url`, `notify_gateway`, "
                + "`notify_status`, `notify_resp`, `notify_retry`, `merchant_sn`, `notify_type`,`notify_payway_type`, `extra`,`ctime`, `mtime`)"
                + " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
        GeneratedKeyHolder keyHolder = new GeneratedKeyHolder();
        enterpriseJdbcTemplate.update(connection -> {
            String[] heads = {"object_type", "object_id", "app_id", "app_secret", "notify_url", "notify_gateway",
                    "notify_status", "notify_resp", "notify_retry", "merchant_sn", "notify_type", "notify_payway_type", "extra", "ctime", "mtime"};
            PreparedStatement preparedStatement = connection.prepareStatement(sql, heads);
            preparedStatement.setString(1,
                    BeanUtil.getPropInt(notifyConfig, NotifyConfig.OBJECT_TYPE, NotifyConfig.OBJECT_TYPE_MERCHANT)
                            + "");
            preparedStatement.setString(2, BeanUtil.getPropString(notifyConfig, NotifyConfig.OBJECT_ID));
            preparedStatement.setString(3, BeanUtil.getPropString(notifyConfig, NotifyConfig.APP_ID));
            preparedStatement.setString(4, BeanUtil.getPropString(notifyConfig, NotifyConfig.APP_SECRET));
            preparedStatement.setString(5, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_URL));
            preparedStatement.setString(6, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_GATEWAY));
            preparedStatement.setString(7, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_STATUS));
            preparedStatement.setString(8, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_RESP));
            preparedStatement.setString(9, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_RETRY));
            preparedStatement.setString(10, BeanUtil.getPropString(notifyConfig, NotifyConfig.MERCHANT_SN));
            preparedStatement.setString(11, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_TYPE));
            preparedStatement.setString(12, BeanUtil.getPropString(notifyConfig, NotifyConfig.NOTIFY_PAYWAY_TYPE));
            Map extraMessage = MapUtil.getMap(notifyConfig, NotifyConfig.EXTRA);
            byte[] extra;
            try {
                extra = objectMapper.writeValueAsString(extraMessage).getBytes(StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new EnterpriseException(SERIALIZATION_ERROR, "extra字段序列化异常");
            }
            preparedStatement.setBinaryStream(13, extra.length != 0 ? new ByteArrayInputStream(extra) : null);
            preparedStatement.setString(14, String.valueOf(System.currentTimeMillis()));
            preparedStatement.setString(15, String.valueOf(System.currentTimeMillis()));
            return preparedStatement;
        }, keyHolder);
    }

    public Dao<Map<String, Object>> getNotifyMessageDao() {
        return notifyMessageDao;
    }

    public void delHistoryMessage(long datetime) {
        enterpriseJdbcTemplate.update("delete from notify_message where push_time < ?", datetime);
    }

    public String removeEmoji(Object source) {
        if(source == null) {
            return null;
        }
        String sourceStr = null;
        if(source instanceof String) {
            sourceStr = (String)source;
        }else if(source instanceof Map) {
            sourceStr = JsonUtil.toJsonStr(source);
        }else {
            sourceStr = String.valueOf(source);
        }
        return sourceStr.replaceAll("[^\\u0000-\\uFFFF]", "");
    }

    //使用NamedParameterJdbcTemplate进行数据的更新
    public void updatePart(Map<String, Object> update) {
        NamedParameterJdbcTemplate npjt = new NamedParameterJdbcTemplate(enterpriseJdbcTemplate);
        Map updatePartInfo = buildUpdatePartSqlAndBindings(update);
        String sql = BeanUtil.getPropString(updatePartInfo, UPDATE_PART_SQL);
        Map binding = (Map) updatePartInfo.get(UPDATE_PART_BINDING);
        npjt.update(sql, binding);
    }

    protected Map<String, Object> buildUpdatePartSqlAndBindings(Map<String, Object> update) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> bindings = new LinkedHashMap<String, Object>();
        sb.append("update `").append("notify_config").append("` set ");
        boolean firstColumn = true;
        for (String prop : update.keySet()) {
            Object value = update.get(prop);
            if (NotifyConfig.OBJECT_ID.equals(prop)) {
                bindings.put(prop, value);
                continue;
            }
            if (!firstColumn) {
                sb.append(", ");
            } else {
                firstColumn = false;
            }
            sb.append(prop).append(" = :").append(prop);
            bindings.put(prop, value);
        }
        sb.append(" where object_id = :object_id");
        String sql = sb.toString();
        return CollectionUtil.hashMap(
                UPDATE_PART_SQL, sql,
                UPDATE_PART_BINDING, bindings
        );
    }

}
