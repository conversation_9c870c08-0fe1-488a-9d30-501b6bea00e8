package com.wosai.bsm.enterprise.controller;

import com.wosai.bsm.enterprise.bean.NotifyConfig;
import com.wosai.bsm.enterprise.util.GatewayAnalysis;
import com.wosai.pantheon.util.MapUtil;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/admin")
public class AdminController {

    /**
     * 获取多个网关统计
     */
    @RequestMapping(value = "/gateway_analysis", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Double> gatewayAnalysis() {
        return GatewayAnalysis.getHostAnalysis();
    }

    /**
     * 删除单个地址的统计
     */
    @RequestMapping(value = "/gateway_analysis/clear", method = RequestMethod.POST)
    @ResponseBody
    public String clearGatewayAnalysis(@RequestBody Map<String, Object> params) {
        GatewayAnalysis.removeHostAnalysis(MapUtil.getString(params, NotifyConfig.NOTIFY_URL));
        return "200";
    }
}
