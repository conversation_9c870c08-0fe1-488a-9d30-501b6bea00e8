package com.wosai.bsm.enterprise.util;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
public class GatewayAnalysis {
    private static LinkedBlockingQueue<GateWay> queue = new LinkedBlockingQueue<GateWay>();
    private static Map<String, Double> hostAnalysis = new ConcurrentHashMap();
    private static AtomicBoolean consume = new AtomicBoolean(true);
    private static int batchSize = 100; // 最多100条执行一次耗时计算
    private static int batchSecond = 3 * 1000; // 最长3秒执行一次耗时计算

    // 创建消费任务
    static{
        new Thread(() ->{
            List<GateWay> batchs = new ArrayList<>(100);
            while(consume.get()){
                long start = System.currentTimeMillis();
                do {
                    GateWay pollGateWay = queue.poll();
                    if(!Objects.isNull(pollGateWay)) {
                        batchs.add(pollGateWay);
                    }else{
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                        }
                    }
                }while(batchs.size() > batchSize || System.currentTimeMillis() - start >= batchSecond);
                if(!batchs.isEmpty()) {
                    batchs
                            .stream()
                            .collect(Collectors.groupingBy(GateWay::getHost, Collectors.averagingLong(GateWay::getCostMs)))
                    .forEach((k, avg) ->{
                        double nextVal = hostAnalysis.getOrDefault(k, avg) + avg;
                        hostAnalysis.put(k, nextVal/2);
                        log.info("change gateway analysis, {}: {}", k, hostAnalysis.get(k));
                    });
                }
                batchs.clear();
            }
        }, "GatewayAnalysis").start();
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            consume.set(false);
        }));
    }

    public static void put(String host, long cost) {
        try {
            queue.put(GateWay.builder()
                    .host(host)
                    .costMs(cost)
                    .build());
        } catch (InterruptedException e) {
            log.warn("put gateway message fail", e);
        }
    }

    public static double getAvg(String host){
        return hostAnalysis.getOrDefault(host, 0D);
    }

    public static boolean isLimit(String host){
        return  getAvg(host) > ApolloUtil.getGatewayLimitTime();
    }

    public static Map<String, Double> getHostAnalysis(){
        return hostAnalysis;
    }

    public static void removeHostAnalysis(String host){
        hostAnalysis.remove(host);
    }
}

@Builder
@Getter
class GateWay{
    String host;
    long costMs;
}