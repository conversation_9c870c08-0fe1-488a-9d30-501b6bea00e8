package com.wosai.bsm.enterprise.controller;

import com.wosai.bsm.enterprise.model.SensitiveProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
@Controller
@RequestMapping("")
public class ApolloConfigValidationController {
    @Autowired
    SensitiveProperties sensitiveProperties;

    @RequestMapping("/testApolloConfig")
    @ResponseBody
    public String testApolloConfig(@RequestParam("key") String key) {
        if (!key.equals("keycnadncpanvvprgnqpifbpiqqnfnqifjnpqiwunfp")) {
            return "密钥错误";
        }

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sensitiveProperties.toString().getBytes(StandardCharsets.UTF_8));
            return new BigInteger(1, md.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密失败：" + e.getMessage(), e);
            return "MD5加密错误";
        }
    }
}
