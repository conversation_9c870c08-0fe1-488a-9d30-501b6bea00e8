<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>enterprise-parent</artifactId>
        <groupId>com.wosai.bsm</groupId>
    <version>1.3.45</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>enterprise</artifactId>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>com.wosai.pay</groupId>
            <artifactId>pay-common-sensitive-apollo</artifactId>
            <version>1.1.6</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>mpay-sdk-homebrew</artifactId>
            <version>2.4.67</version>
            <exclusions>
                <exclusion>
                    <artifactId>wosai-common</artifactId>
                    <groupId>com.wosai.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mis</artifactId>
                    <groupId>com.ccb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.5.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-side-api</artifactId>
            <version>1.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>merchant-contract-access-api</artifactId>
            <version>1.5.14</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>zeus-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>video-service-api</artifactId>
                    <groupId>com.wosai.app</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.ccb</groupId>
                    <artifactId>mis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>mpay-sdk-homebrew</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>kotlin-stdlib</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kotlin-stdlib-common</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kotlin-stdlib-jdk8</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tomcat-embed-el</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay.upay-trade</groupId>
            <artifactId>upay-trade-api</artifactId>
            <version>1.2.13</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>crm-databus</artifactId>
            <version>0.24.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-databus</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus-api</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.iot</groupId>
            <artifactId>shouqianba-iot-api</artifactId>
            <version>3.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcprov-jdk15</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.device</groupId>
                    <artifactId>ota-service-utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mylomen</groupId>
                    <artifactId>sql-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.4</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.bsm</groupId>
            <artifactId>enterprise-api</artifactId>
        <version>1.3.45</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-service-api</artifactId>
            <version>0.5.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
          <groupId>com.wosai.upay</groupId>
          <artifactId>upay-transaction-api</artifactId>
            <version>1.8.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <version>2.2.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>vault-sdk</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-spring</artifactId>
            <version>5.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>apollo-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.11.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

         <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>${jsonrpc4j.version}</version>
         </dependency>


        <!-- wosai -->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>3.9.14</version>
        </dependency>

        <!-- web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- ehcache -->
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>2.10.1</version>
        </dependency>

        <!-- database -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-dbcp2</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-jdbc</artifactId>
            <version>${nextgen.version}</version>
        </dependency>

        <!-- commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.4</version>
        </dependency>

        <!--log-->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.bsm</groupId>
            <artifactId>trial</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
          <groupId>com.wosai.upay</groupId>
          <artifactId>user-service-api</artifactId>
          <version>1.0.3-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <groupId>com.github.briandilley.jsonrpc4j</groupId>
                  <artifactId>jsonrpc4j</artifactId>
              </exclusion>
          </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>1.0.0-cp1</version>
        </dependency>
        <!-- avro -->
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>${avro.version}</version>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-avro-serializer</artifactId>
            <version>${confluent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-schema-registry-client</artifactId>
            <version>${confluent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>common-config</artifactId>
            <version>${confluent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>common-utils</artifactId>
            <version>3.3.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
            <version>1.1.15</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-util</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.9.6</version>
        </dependency>

        <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-redis</artifactId>
            <version>1.7.5.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.8.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.12</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>vault-sdk</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.40.104.ALL</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
            <version>1.10.12</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.uc</groupId>
            <artifactId>uc-token-api</artifactId>
            <version>1.5.6</version>
        </dependency>


        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.11.6</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay.upay-prepaid-card</groupId>
            <artifactId>upay-prepaid-api</artifactId>
            <version>1.6.8</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>lark-chatbot-sdk</artifactId>
            <version>0.1.5</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>enterprise</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>8</target>
                    <source>8</source>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.8.v20171121</version>
                <configuration>
                    <webApp>
                        <contextPath>/</contextPath>
                    </webApp>
                    <systemProperties>
                        <!--<systemProperty>-->
                        <!--<name>shouqianba.flavor</name>-->
                        <!--<value>test</value>-->
                        <!--</systemProperty>-->
                    </systemProperties>
                    <httpConnector>
                        <port>11146</port>
                    </httpConnector>
                    <stopKey>alpha</stopKey>
                    <stopPort>9099</stopPort>
                </configuration>
            </plugin>


            <plugin>

                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.3.0-SNAPSHOT</version>
                <configuration>
                    <properties>
                        <property>
                            <!-- 这个resource定义文档中需要定义的变量, #{} 为变量名 -->
                            <resource>spring/flavor-#{shouqianba.flavor:-default}.properties</resource>
                        </property>
                    </properties>
                    <startWithSpringBoot>false</startWithSpringBoot>
                    <enableCallerData>true</enableCallerData>
                    <root>
                        <references>
                            <!-- #{} 为上面resource中定义的变量名变量名，下面的输出类型可以为 FT_CONSOLE_JSON，FT_FILE，FT_CONSOLE_PATTERN -->
                            <ref>#{logback.rootAppender}</ref>
                        </references>
                    </root>
                    <scopes>
                        <scope>
                            <name>com.wosai.mpay</name>
                            <level>DEBUG</level>
                        </scope>
                    </scopes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>
        </plugins>
    </build>

</project>