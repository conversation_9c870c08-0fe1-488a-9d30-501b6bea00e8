<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wosai.bsm</groupId>
    <artifactId>enterprise-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.3.45</version>
    <modules>
        <module>enterprise-api</module>
        <module>enterprise</module>
    </modules>

    <properties>
        <spring.boot.version>1.4.0.RELEASE</spring.boot.version>
        <jsonrpc4j.version>2.2.4-alpha</jsonrpc4j.version>
        <cglib-nodep.version>2.2</cglib-nodep.version>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
        <confluent.version>4.0.0</confluent.version>
        <avro.version>1.8.2</avro.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>${cglib-nodep.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

</project>