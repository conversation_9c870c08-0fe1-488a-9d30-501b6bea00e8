#!/bin/sh

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"core-business","instance_name":"srds-aimt-bs-total","database_name":"upay_log","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"core-business","instance_name":"srds-aimt-bs-total","database_name":"upay_user","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"core-business","instance_name":"srds-aimt-bs-total","database_name":"merchant_contract","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"core-business","instance_name":"srds-aimt-bs-total","database_name":"upay_bank_info","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"upay-gateway","instance_name":"srds-aimt-bs-total","database_name":"upay","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"upay-gateway","instance_name":"srds-aimt-bs-total","database_name":"ticket","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"upay-side","instance_name":"srds-aimt-bs-total","database_name":"upay_side","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"upay-grayscale","instance_name":"srds-aimt-bs-total","database_name":"upay_related","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"trade-manage-service","instance_name":"srds-aimt-bs-total","database_name":"trade_manage","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"upay-transaction","instance_name":"srds-aimt-bs-total","database_name":"upay_transaction","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"trade-analysis","instance_name":"srds-aimt-bs-total","database_name":"trade_analysis","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"trade-analysis","instance_name":"srds-aimt-bs-total","database_name":"withdraw_service","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"sms-gateway","instance_name":"srds-aimt-bs-total","database_name":"sms_gateway","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"short-url","instance_name":"srds-aimt-bs-total","database_name":"short_link","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"upay-qrcode","instance_name":"srds-aimt-bs-total","database_name":"upay_qrcode","type":"1","note":"aimt application init"}'

 
curl 'https://dbmanager-adv.wosai-inc.com/api/assets/apply-for-application' \
-X 'POST' \
-H 'Content-Type: application/json;charset=utf-8' \
-H 'Accept: application/json, text/plain, */*' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Accept-Encoding: gzip, deflate, br' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Origin: https://dbmanager-adv.wosai-inc.com' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Safari/605.1.15' \
-H 'Referer: https://dbmanager-adv.wosai-inc.com/assets/add/mysql/application' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Cookie: _ga=GA1.2.*********.**********; experimentation_subject_id=IjgzNjhiYWMzLTY0YTEtNDYzNi1hYTBlLTM1YzVmMTg0ZDQwZSI%3D--d3a562c893ebbe515a54c6cfb7241a0ebbb5a9db' \
-H 'Priority: u=3, i' \
-H 'Access-Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************.K-XmZVV7j34464Gr8a3exRQaBrx_pi28KSF3xc73pOI' \
--data-binary '{"instance_type":0,"service_name":"enterprise","instance_name":"srds-aimt-bs-total","database_name":"enterprise","type":"1","note":"aimt application init"}'

